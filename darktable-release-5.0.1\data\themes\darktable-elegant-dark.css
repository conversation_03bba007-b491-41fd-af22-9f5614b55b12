/*
    This file is part of darktable,
    copyright (c) 2021 <PERSON>

    darktable is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    darktable is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    ME<PERSON><PERSON><PERSON><PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with darktable.  If not, see <http://www.gnu.org/licenses/>.
*/

@import url("darktable-elegant-darker.css");

/* This has been tested with GTK 3.24 on Gnome */

/* General */
@define-color selected_bg_color @grey_50;       /* legacy stuff */
@define-color border_color @grey_25;            /* border, when used */
@define-color bg_color @grey_30;                /* general background */
@define-color fg_color @grey_80;                /* general text */
@define-color text_color @grey_20;              /* same */
@define-color placeholder_text_color @grey_60;  /* placeholder text color (text on search background fields) */
@define-color disabled_fg_color @grey_55;       /* disabled controls */

/* Scroll bars (sliders) */
@define-color scroll_bar_inactive @grey_50;
@define-color scroll_bar_active @grey_70;
@define-color scroll_bar_bg @grey_25;

/* Modules box (plugins) */
@define-color plugin_bg_color @grey_35;
@define-color plugin_label_color @grey_70;
@define-color collapsible_bg_color @grey_40;

/* Modules controls (sliders and comboboxes) */
@define-color bauhaus_indicator_border @grey_35;
@define-color bauhaus_fill @grey_55;
@define-color bauhaus_bg_hover @grey_70;
@define-color bauhaus_fg_hover @grey_95;
@define-color bauhaus_fg_selected @grey_85;

/* GTK Buttons and tabs */
@define-color button_bg @grey_40;
@define-color button_hover_bg @grey_65;
@define-color button_hover_fg @grey_25;

/* text fields */
@define-color field_bg @grey_30;
@define-color field_active_bg @grey_40;
@define-color field_active_fg @grey_90;
@define-color field_selected_bg @grey_50;
@define-color field_selected_fg @grey_95;
@define-color field_hover_bg @grey_65;
@define-color field_hover_fg @grey_20;

/* Tooltips and contextual helpers */
@define-color tooltip_bg_color @grey_25;
@define-color tooltip_fg_color @grey_75;
@define-color log_fg_color @grey_90;

/* Views */
@define-color lighttable_bg_color @grey_45;
@define-color lighttable_bg_font_color @grey_85;

/* Lighttable and film-strip */
@define-color thumbnail_bg_color @grey_55;
@define-color thumbnail_star_bg_color @grey_40;
@define-color thumbnail_star_hover_color @grey_85;
@define-color thumbnail_fg_color @grey_60;
@define-color thumbnail_selected_bg_color @grey_75;
@define-color thumbnail_hover_bg_color @grey_90;
@define-color thumbnail_hover_fg_color @grey_95;
@define-color thumbnail_localcopy_color @grey_80;

@define-color range_bg_color alpha(@fg_color, 0.1);

/* Graphs : histogram, navigation thumbnail and some items on tone curve */
@define-color graph_bg @grey_25;
@define-color graph_border @grey_20;
@define-color graph_fg @grey_90;
@define-color graph_grid @grey_20;
@define-color inset_histogram alpha(@grey_80, 0.50);

@define-color colorlabel_blue rgb(0,40,255);

/* Set background on thumbnails hover overlays */
.dt_overlays_hover_extended #thumb-main:hover #thumb-bottom,
.dt_overlays_mixed #thumb-main:hover #thumb-bottom,
.dt_overlays_hover #thumb-main:hover #thumb-bottom
{
  background-image: linear-gradient(rgba(226, 226, 226, 0.7) 0%, rgba(226, 226, 226, 0.7) 90%,rgba(226, 226, 226, 0) 100%);  /* rgb color needs to be set to same color as #thumb_back hover background */
}

/* Set how bottom infos are rendered on always and always extended overlays modes in culling and preview modes */
.dt_overlays_always#culling #thumb-bottom,
.dt_overlays_always_extended#culling #thumb-bottom,
.dt_overlays_always#preview #thumb-bottom,
.dt_overlays_always_extended#preview #thumb-bottom
{
  background-color: shade(@lighttable_bg_color, 1.05);
}

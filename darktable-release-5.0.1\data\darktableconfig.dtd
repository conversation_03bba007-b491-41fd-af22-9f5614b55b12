<!ELEMENT dtconfiglist (dttab*, dtconfig*)>

<!ELEMENT dttab (section)+>
<!ATTLIST dttab
	name ID #REQUIRED
        title CDATA #REQUIRED
>
<!ELEMENT section EMPTY>
<!ATTLIST section
	name ID #IMPLIED
        title CDATA #REQUIRED
>

<!ELEMENT dtconfig (name,type,default,capability?,shortdescription?,longdescription?)>
<!ATTLIST dtconfig
	prefs IDREF #IMPLIED
        section IDREF #IMPLIED
        dialog (collect|recentcollect|import|tagging) #IMPLIED
        ui (yes|no) #IMPLIED
        restart (true|false) #IMPLIED
        capability CDATA #IMPLIED
>
<!ELEMENT name (#PCDATA)>
<!ELEMENT type (#PCDATA|enum)*>
<!ATTLIST type
	min CDATA #IMPLIED
	max CDATA #IMPLIED
	factor CDATA #IMPLIED
>
<!ELEMENT enum (option)+>
<!ELEMENT option (#PCDATA)>
<!ATTLIST option
	capability CDATA #IMPLIED
>
<!ELEMENT default (#PCDATA)>
<!ELEMENT shortdescription (#PCDA<PERSON>)>
<!ELEMENT longdescription (#PCDATA)>

<?xml version="1.0"?>
<!DOCTYPE dtconfiglist SYSTEM "darktableconfig.dtd">
<dtconfiglist>
  <dttab name="import" title="import">
    <section name="session" title="session options"/>
  </dttab>
  <dttab name="lighttable" title="lighttable">
    <section title="general"/>
    <section name="thumbs" title="thumbnails"/>
  </dttab>
  <dttab name="darkroom" title="darkroom">
    <section title="general"/>
    <section name="modules" title="modules"/>
  </dttab>
  <dttab name="processing" title="processing">
    <section name="general" title="image processing"/>
    <section name="cpugpu" title="CPU / memory"/>
    <section name="opencl" title="OpenCL GPU acceleration"/>
    <section name="platform" title="OpenCL drivers"/>
  </dttab>
  <dttab name="security" title="security">
    <section title="general"/>
    <section name="other" title="other"/>
  </dttab>
  <dttab name="storage" title="storage">
    <section name="database" title="database"/>
    <section name="XMP" title="XMP sidecar files"/>
  </dttab>
  <dttab name="misc" title="miscellaneous">
    <section name="interface" title="interface"/>
    <section name="tags" title="tags"/>
    <section name="accel" title="shortcuts with multiple instances"/>
    <section name="geoloc" title="map / geolocalization view"/>
    <section name="slideshow" title="slideshow view"/>
  </dttab>

  <dtconfig>
    <name>themes/usercss</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>modify theme with user tweaks</shortdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="accel">
    <name>accel/prefer_focused</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>prefer focused instance</shortdescription>
    <longdescription>where multiple instances of a module are present, apply shortcuts to the instance that has focus\nif none are focused, the preferences below control rules that are followed (in order) to decide which module instance shortcuts will be applied to.\nnote: blending shortcuts always apply to the focused instance</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="accel">
    <name>accel/prefer_expanded</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>prefer expanded instances</shortdescription>
    <longdescription>if instances of the module are expanded, ignore collapsed instances</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="accel">
    <name>accel/prefer_enabled</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>prefer enabled instances</shortdescription>
    <longdescription>after applying the above rule, if instances of the module are active, ignore inactive instances</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="accel">
    <name>accel/prefer_unmasked</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>prefer unmasked instances</shortdescription>
    <longdescription>after applying the above rules, if instances of the module are unmasked, ignore masked instances</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="accel">
    <name>accel/select_order</name>
    <type>
      <enum>
        <option>first instance</option>
        <option>last instance</option>
      </enum>
    </type>
    <default>last instance</default>
    <shortdescription>selection order</shortdescription>
    <longdescription>after applying the above rules, apply the shortcut based on its position in the pixelpipe</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="accel">
    <name>accel/assign_instance</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>allow visual assignment to specific instances</shortdescription>
    <longdescription>when multiple instances are present on an image this allows shortcuts to be visually assigned to those specific instances\notherwise shortcuts will always be assigned to the preferred instance</longdescription>
  </dtconfig>
  <dtconfig>
    <name>accel/slider_precision</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>keyboard shortcut slider precision</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>accel/enable_fallbacks</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>enable shortcut fallbacks</shortdescription>
    <longdescription>enables default meanings for additional buttons, modifiers or moves when used in combination with a base shortcut</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="interface">
    <name>show_splash_screen</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>show splash screen at startup</shortdescription>
    <longdescription>display a small window showing the progress of darktable startup before the main window appears</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="interface">
    <name>accel/load_defaults</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>load default shortcuts at startup</shortdescription>
    <longdescription>load default shortcuts before user settings. switch off to prevent deleted defaults returning</longdescription>
  </dtconfig>
  <dtconfig>
    <name>accel/show_tab_in_prefs</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>show the shortcuts configuration tab in the preferences dialog</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>accel/hide_notice</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>don't show advice in shortcuts dialog</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>bauhaus/scale</name>
    <type>float</type>
    <default>1.4</default>
    <shortdescription>widget scale</shortdescription>
    <longdescription>scaling factor for bauhaus widgets, will affect font size</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="interface">
    <name>bauhaus/zoom_step</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>scale slider step with min/max</shortdescription>
    <longdescription>vary slider step size with min/max range</longdescription>
  </dtconfig>
  <dtconfig>
    <name>database</name>
    <type>string</type>
    <default>library.db</default>
    <shortdescription>database location</shortdescription>
    <longdescription>filename relative to ~/.config/darktable or starting with a slash (restart required)</longdescription>
  </dtconfig>
  <dtconfig>
    <name>storage/piwigo/last_album</name>
    <type>string</type>
    <default></default>
    <shortdescription></shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>storage/piwigo/overwrite</name>
    <type>int</type>
    <default>0</default>
    <shortdescription></shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>database/maintenance_freepage_ratio</name>
    <type>int</type>
    <default>25</default>
    <shortdescription>database fragmentation ratio threshold</shortdescription>
    <longdescription>fragmentation ratio above which to ask or carry out automatically database maintenance</longdescription>
  </dtconfig>
  <dtconfig prefs="storage" section="database">
    <name>database/create_snapshot</name>
    <type>
      <enum>
        <option>never</option>
        <option>once a month</option>
        <option>once a week</option>
        <option>once a day</option>
        <option>on close</option>
      </enum>
    </type>
    <default>once a week</default>
    <shortdescription>create database snapshot</shortdescription>
    <longdescription>database snapshots are created right before closing darktable. options allow you to choose how often to make snapshots:\n - 'never': simply don't do snapshots. that way the only snapshots done are mandatory version-upgrade snapshots\n - 'once a month': create snapshot if a month has passed since last snapshot\n - 'once a week': create snapshot if 7 days had passed since last snapshot\n - 'once a day': create snapshot if over 24h passed since last snapshot\n - 'on close': create snapshot every time darktable is closed</longdescription>
  </dtconfig>
  <dtconfig prefs="storage" section="database">
    <name>database/keep_snapshots</name>
    <type>int</type>
    <default>10</default>
    <shortdescription>how many snapshots to keep</shortdescription>
    <longdescription>after successfully creating snapshot, how many older snapshots to keep (excluding mandatory version update ones). enter -1 to keep all snapshots\nkeep in mind that snapshots do take some space and you only need the most recent one for successful restore</longdescription>
  </dtconfig>
  <dtconfig>
    <name>min_panel_height</name>
    <type>int</type>
    <default>64</default>
    <shortdescription>minimum height of the bottom panel in pixels</shortdescription>
    <longdescription>(restart required)</longdescription>
  </dtconfig>
  <dtconfig>
    <name>max_panel_height</name>
    <type>int</type>
    <default>400</default>
    <shortdescription>maximum height of the bottom panel in pixels</shortdescription>
    <longdescription>(restart required)</longdescription>
  </dtconfig>
  <dtconfig>
    <name>min_panel_width</name>
    <type>int</type>
    <default>150</default>
    <shortdescription>minimum width of the side panels in pixels</shortdescription>
    <longdescription>(restart required)</longdescription>
  </dtconfig>
  <dtconfig>
    <name>max_panel_width</name>
    <type>int</type>
    <default>1500</default>
    <shortdescription>maximum width of the side panels in pixels</shortdescription>
    <longdescription>(restart required)</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="slideshow">
    <name>slideshow_delay</name>
    <type>int</type>
    <default>5</default>
    <shortdescription>waiting time between each image in slideshow</shortdescription>
  </dtconfig>
<!-- be sure to keep the code in sync when changing this enum, see common/darktable.c void dt_get_sysresource_level() -->
  <dtconfig prefs="processing" section="cpugpu">
    <name>resourcelevel</name>
    <type>
      <enum>
        <option>small</option>
        <option>default</option>
        <option>large</option>
      </enum>
    </type>
    <default>default</default>
    <shortdescription>darktable resources</shortdescription>
    <longdescription>defines how much darktable may take from your system resources:\n - 'default': darktable takes ~50% of your systems resources, which is enough to be performant.\n - 'small': should be used if you are simultaneously running applications taking large parts of your systems memory or OpenCL/GL applications like games or Hugin.\n - 'large': is the best option if you are not running other applications at the same time as darktable and want it to take most of your systems resources for performance.</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="cpugpu">
    <name>ui/performance</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>prefer performance over quality</shortdescription>
    <longdescription>if switched on, thumbnails and previews are rendered at lower quality but 4 times faster</longdescription>
  </dtconfig>
  <dtconfig>
    <name>backthumbs_inactivity</name>
    <type>float</type>
    <default>5.0</default>
    <shortdescription>inactivity time</shortdescription>
    <longdescription>user inactivity time (seconds) while being in lighttable before a thumbnail generation might start</longdescription>
  </dtconfig>
  <dtconfig>
    <name>cache_color_managed</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>color manage cached thumbnails</shortdescription>
    <longdescription>if enabled, cached thumbnails will be color managed so that lighttable and filmstrip can show correct colors. otherwise the results may look wrong once the display profile gets changed.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>opencl_device_priority</name>
    <type>string</type>
    <default>*/!0,*/*/*/!0,*</default>
    <shortdescription>priority of OpenCL devices for each pixelpipe type</shortdescription>
    <longdescription>defines priorities on how (multiple) OpenCL devices are allocated to the different types of pixelpipe (full, preview, export, thumbnail, preview2). for more details visit our user manual. (restart required)</longdescription>
  </dtconfig>
  <dtconfig>
    <name>opencl_mandatory_timeout</name>
    <type min="100">int</type>
    <default>1000</default>
    <shortdescription>timeout period for locking mandatory opencl device</shortdescription>
    <longdescription>time period (in units of 5ms) after which we give up try-locking an opencl device for mandatory use. defaults to 400 (2 seconds).</longdescription>
  </dtconfig>
  <dtconfig>
    <name>opencl_checksum</name>
    <type>string</type>
    <default></default>
    <shortdescription>checksum representing the setup of opencl devices on this computer</shortdescription>
    <longdescription>darktable re-checks the performance benchmarks of your system in case your setup has changed, which is indicated by a change versus the stored checksum in this config variable; darktable de-activates opencl if the GPU benchmark lies below the one of the CPU; initial value is the empty string; set to OFF if you want to deactivate any automatic checks and prefer to do all configurations manually.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>pixelpipe_synchronization_timeout</name>
    <type>int</type>
    <default>200</default>
    <shortdescription>timeout period of pixelpipe synchronization</shortdescription>
    <longdescription>time period (in units of 5ms) after which synchronization of preview and full pixelpipe is assumed to have failed. set to zero to omit pixelpipe synchronization. defaults to 200.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>libraw_extensions</name>
    <type>string</type>
    <default></default>
    <shortdescription>raw file extensions to be processed by libraw</shortdescription>
    <longdescription>space-delimited list of raw file extensions (without a leading dot, in lowercase) to be processed by libraw instead of rawspeed.</longdescription>
  </dtconfig>
  <dtconfig prefs="storage" section="XMP">
    <name>write_sidecar_files</name>
    <type>
      <enum>
        <option>never</option>
        <option>after edit</option>
        <option>on import</option>
      </enum>
    </type>
    <default>on import</default>
    <shortdescription>create XMP files</shortdescription>
    <longdescription>XMP sidecar files hold information about all your development steps to allow flawless re-importing of image files.\n\ndepending on the selected mode sidecar files will be created:\n - 'never': all development information will be stored only in the library database\n - 'on import': immediately after importing the image\n - 'after edit': after any user change on the image or adding tags.</longdescription>
  </dtconfig>
  <dtconfig prefs="storage" section="XMP">
    <name>compress_xmp_tags</name>
    <type>
      <enum>
        <option>never</option>
        <option>always</option>
        <option>only large entries</option>
      </enum>
    </type>
    <default>only large entries</default>
    <shortdescription>store XMP tags in compressed format</shortdescription>
    <longdescription>entries in XMP tags can get rather large and may exceed the available space to store the history stack in output files.\nthis option allows XMP tags to be compressed and save space.</longdescription>
  </dtconfig>
  <dtconfig prefs="storage" section="XMP">
    <name>autosave_interval</name>
    <type>int</type>
    <default>10</default>
    <shortdescription>auto-save interval</shortdescription>
    <longdescription>automatically save history while developing using the given interval (in seconds); set to zero to disable auto-saving. auto-saving might be disabled on slow drives.</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="tags">
    <name>omit_tag_hierarchy</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>omit hierarchy in simple tag lists</shortdescription>
    <longdescription>when creating an XMP sidecar file the hierarchical tags are also added as a simple list\nof non-hierarchical ones to make them visible to some other programs.\nwhen this option is checked darktable will only include their last part\nand ignore the rest. so 'foo|bar|baz' will only add 'baz'.</longdescription>
  </dtconfig>
  <dtconfig dialog="collect">
    <name>plugins/lighttable/tagging/no_uncategorized</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>do not set the 'uncategorized' entry for tags</shortdescription>
    <longdescription>do not set the 'uncategorized' entry for tags which do not have children</longdescription>
  </dtconfig>
  <dtconfig dialog="collect">
    <name>plugins/lighttable/tagging/case_sensitivity</name>
    <type>
      <enum>
        <option>sensitive</option>
        <option>insensitive</option>
      </enum>
    </type>
    <default>insensitive</default>
    <shortdescription>tags case sensitivity</shortdescription>
    <longdescription>tags case sensitivity. without the Sqlite ICU extension, insensitivity works only for the 26 latin letters</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="opencl" capability="opencl">
    <name>opencl</name>
    <type>bool</type>
    <default>${DEFCONFIG_OPENCL}</default>
    <shortdescription>activate OpenCL support</shortdescription>
    <longdescription>if found, use OpenCL runtime on your system to speed up processing by using your graphics card(s).\ncan be switched on and off at any time.</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="opencl" capability="opencl">
    <name>opencl_scheduling_profile</name>
    <type>
      <enum>
        <option>default</option>
        <option>multiple GPUs</option>
        <option>very fast GPU</option>
      </enum>
    </type>
    <default>default</default>
    <shortdescription>OpenCL scheduling profile</shortdescription>
    <longdescription>defines how preview and full pixelpipe tasks are scheduled on OpenCL enabled systems:\n - 'default': GPU processes full and CPU processes preview pipe (adaptable by config parameters),\n - 'multiple GPUs': process both pixelpipes in parallel on two different GPUs,\n - 'very fast GPU': process both pixelpipes sequentially on the GPU.</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="opencl" capability="opencl">
    <name>opencl_tune_headroom</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>use all GPU memory</shortdescription>
    <longdescription>if enabled darktable will use all graphics device memory except a safety margin (headroom, default is 600MB)</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="platform" capability="nonapple" restart="true">
    <name>clplatform_intelropenclhdgraphics</name>
    <type>bool</type>
    <default>${DEFCONFIG_NONAPPLE}</default>
    <shortdescription>Intel GPU</shortdescription>
    <longdescription>Intel(R) OpenCL Graphics for all supported platforms (vendor provided)</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="platform" capability="nonapple" restart="true">
    <name>clplatform_nvidiacuda</name>
    <type>bool</type>
    <default>${DEFCONFIG_NONAPPLE}</default>
    <shortdescription>Nvidia CUDA</shortdescription>
    <longdescription>Nvidia CUDA based OpenCL (vendor provided)</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="platform" capability="nonapple" restart="true">
    <name>clplatform_amdacceleratedparallelprocessing</name>
    <type>bool</type>
    <default>${DEFCONFIG_NONAPPLE}</default>
    <shortdescription>AMD ROCm</shortdescription>
    <longdescription>AMD Accelerated Parallel Processing (vendor provided)</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="platform" capability="nonapple" restart="true">
    <name>clplatform_rusticl</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>RustiCL (experimental)</shortdescription>
    <longdescription>RustiCL Mesa OpenCL, still unstable. if you want to use this, you should disable the vendor driver</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="platform" capability="apple" restart="true">
    <name>clplatform_apple</name>
    <type>bool</type>
    <default>${DEFCONFIG_APPLE}</default>
    <shortdescription>Apple</shortdescription>
    <longdescription>Apple OpenCL (vendor provided)</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="platform" capability="windows" restart="true">
    <name>clplatform_openclon12</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>Microsoft OpenCLOn12</shortdescription>
    <longdescription>Microsoft OpenCLOn12, only use this if the vendor provided driver does not work or there is none provided.</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="platform" restart="true">
    <name>clplatform_other</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>other platforms</shortdescription>
    <longdescription>if set, all unspecified platforms are accepted. only do this if no vendor driver is available</longdescription>
  </dtconfig>
  <dtconfig>
    <name>opencl_library</name>
    <type>string</type>
    <default/>
    <shortdescription>system library with OpenCL runtime</shortdescription>
    <longdescription>OpenCL runtime library is normally detected automatically by darktable. if your OpenCL runtime is at an unusual place and cannot be detected, enter the full pathname here. leave empty for default behavior.</longdescription>
  </dtconfig>
  <dtconfig prefs="security" section="general">
    <name>ask_before_remove</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>ask before removing images from the library</shortdescription>
    <longdescription>always ask the user before removing image information from the library</longdescription>
  </dtconfig>
  <dtconfig prefs="security" section="general">
    <name>ask_before_delete</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>ask before deleting images from disk</shortdescription>
    <longdescription>always ask the user before any image file is deleted</longdescription>
  </dtconfig>
  <dtconfig prefs="security" section="general">
    <name>ask_before_discard</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>ask before discarding history stack</shortdescription>
    <longdescription>always ask the user before history stack is discarded on any image</longdescription>
  </dtconfig>
  <dtconfig prefs="security" section="general">
    <name>send_to_trash</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>try to use trash when deleting images</shortdescription>
    <longdescription>send files to trash instead of permanently deleting files on system that supports it</longdescription>
  </dtconfig>
  <dtconfig prefs="security" section="general">
    <name>ask_before_move</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>ask before moving images from film roll folder</shortdescription>
    <longdescription>always ask the user before any image file is moved.</longdescription>
  </dtconfig>
  <dtconfig prefs="security" section="general">
    <name>ask_before_copy</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>ask before copying images to new film roll folder</shortdescription>
    <longdescription>always ask the user before any image file is copied.</longdescription>
  </dtconfig>
  <dtconfig prefs="security" section="general">
    <name>ask_before_rmdir</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>ask before removing empty folders</shortdescription>
    <longdescription>always ask the user before removing any empty folder. this can happen after moving or deleting images.</longdescription>
  </dtconfig>
  <dtconfig dialog="recentcollect">
    <name>plugins/lighttable/recentcollect/max_items</name>
    <type min="1" max="50">int</type>
    <default>10</default>
    <shortdescription>number of collections to be stored</shortdescription>
    <longdescription>the number of recent collections to store and show in this list</longdescription>
  </dtconfig>
  <dtconfig dialog="collect">
    <name>plugins/lighttable/collect/history_max</name>
    <type min="1" max="50">int</type>
    <default>10</default>
    <shortdescription>number of collections to be stored</shortdescription>
    <longdescription>the number of recent collections to store and show in this list</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/history_max</name>
    <type min="1" max="50">int</type>
    <default>10</default>
    <shortdescription>number of filters to be stored</shortdescription>
    <longdescription>the number of recent filters to store and show in history list</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/sort_history_max</name>
    <type min="1" max="50">int</type>
    <default>10</default>
    <shortdescription>number of sort orders to be stored</shortdescription>
    <longdescription>the number of recent sort orders to store and show in history list</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/num_sort</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>nb of sort orders by default</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/sort0</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>first sort orders</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/num_rules</name>
    <type min="1" max="12">int</type>
    <default>3</default>
    <shortdescription>nb of rules by default</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/item0</name>
    <type>int</type>
    <default>32</default>
    <shortdescription>first filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/mode0</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>first filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/off0</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>first filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/top0</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>first filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/string0</name>
    <type>string</type>
    <default>%</default>
    <shortdescription>first filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/item1</name>
    <type>int</type>
    <default>18</default>
    <shortdescription>second filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/mode1</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>second filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/off1</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>second filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/top1</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>second filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/string1</name>
    <type>string</type>
    <default></default>
    <shortdescription>second filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/item2</name>
    <type>int</type>
    <default>33</default>
    <shortdescription>third filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/mode2</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>third filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/off2</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>third filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/top2</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>third filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/filtering/string2</name>
    <type>string</type>
    <default>%%</default>
    <shortdescription>third filter</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig dialog="collect">
    <name>show_folder_levels</name>
    <type min="1" max="5">int</type>
    <default>1</default>
    <shortdescription>number of folder levels to show in lists</shortdescription>
    <longdescription>the number of folder levels to show in film roll names, starting from the right</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/collection/film_id</name>
    <type>int</type>
    <default>1</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/collection/rating</name>
    <type>int</type>
    <default>1</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/collection/rating_comparator</name>
    <type>int</type>
    <default>3</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/collection/query_flags</name>
    <type>int</type>
    <default>3</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/collection/filter_flags</name>
    <type>int</type>
    <default>3</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/collection/colors_filter</name>
    <type>string</type>
    <default>80000000</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/collection/text_filter</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/collection/descending</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/collection/sort</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/session/jobcode</name>
    <type>string</type>
    <default>capture job</default>
    <shortdescription>name of capture job</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/capture/camera/live_view_fps</name>
    <type>int</type>
    <default>15</default>
    <shortdescription>maximum fps of live view update in tethering view</shortdescription>
    <longdescription>going too fast will result in too many redraws without a real benefit</longdescription>
  </dtconfig>
  <dtconfig dialog="collect">
    <name>plugins/collect/filmroll_sort</name>
    <type>
      <enum>
        <option>import time</option>
        <option>folder name</option>
      </enum>
    </type>
    <default>import time</default>
    <shortdescription>sort film rolls by</shortdescription>
    <longdescription>sets the collections-list order for film rolls</longdescription>
  </dtconfig>
  <dtconfig>
    <name>ui_last/colorpicker_model</name>
    <type>
      <enum>
	<option>RGB</option>
	<option>Lab</option>
	<option>LCh</option>
	<option>HSL</option>
	<option>Hex</option>
	<option>none</option>
      </enum>
    </type>
    <default>RGB</default>
    <shortdescription>color model with which to represent primary colorpicker</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/colorpicker_mode</name>
    <type>
      <enum>
	<option>mean</option>
	<option>min</option>
	<option>max</option>
      </enum>
    </type>
    <default>mean</default>
    <shortdescription>statistic used to describe colorpicker box</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/colorpicker_display_samples</name>
    <type>boolean</type>
    <default>false</default>
    <shortdescription>display live sample areas on image</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/colorpicker_restrict_histogram</name>
    <type>boolean</type>
    <default>false</default>
    <shortdescription>restrict histogram to selection</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/colorpicker_large</name>
    <type>boolean</type>
    <default>false</default>
    <shortdescription>display large colorpicker patch</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/fullscreen</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/maximized</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/grouping</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>grouping of images</shortdescription>
    <longdescription>only show a single image for each set of grouped images in lighttable and filmstrip</longdescription>
  </dtconfig>
  <dtconfig>
    <name>ui_last/view</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/window_x</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/window_y</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/window_w</name>
    <type>int</type>
    <default>900</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/window_h</name>
    <type>int</type>
    <default>500</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/panel_left</name>
    <type>int</type>
    <default>-1</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/panel_right</name>
    <type>int</type>
    <default>-1</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/panel_top</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/panel_bottom</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/expander_import</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/import_dialog_paned_pos</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/import_dialog_paned_places_pos</name>
    <type>int</type>
    <default>150</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/import_dialog_show_home</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/import_dialog_show_pictures</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/import_dialog_show_mounted</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/import_dialog_width</name>
    <type>int</type>
    <default>800</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/import_dialog_height</name>
    <type>int</type>
    <default>600</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/shortcuts_dialog_width</name>
    <type>int</type>
    <default>1100</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/shortcuts_dialog_height</name>
    <type>int</type>
    <default>700</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/preferences_dialog_width</name>
    <type>int</type>
    <default>1100</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/preferences_dialog_height</name>
    <type>int</type>
    <default>720</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/modulegroups_dialog_width</name>
    <type>int</type>
    <default>1100</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/modulegroups_dialog_height</name>
    <type>int</type>
    <default>700</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/session_expander_import</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/import_select_new</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>select only new images</shortdescription>
    <longdescription>only select images that have not already been imported</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/import_ignore_nonraws</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>ignore non-raw images</shortdescription>
    <longdescription>if enabled, only raw files will be allowed to import. non-raw files will not be visible in the dialog and will not be imported.</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/import_apply_metadata</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>apply metadata</shortdescription>
    <longdescription>apply some metadata to all newly imported images.</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/import_recursive</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>recursive directory</shortdescription>
    <longdescription>recursive directory traversal when importing filmrolls</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/import_last_creator</name>
    <type>string</type>
    <default/>
    <shortdescription>creator to be applied when importing</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/import_last_publisher</name>
    <type>string</type>
    <default/>
    <shortdescription>publisher to be applied when importing</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/import_last_rights</name>
    <type>string</type>
    <default/>
    <shortdescription>rights to be applied when importing</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/import_last_tags</name>
    <type>string</type>
    <default/>
    <shortdescription>comma separated tags to be applied when importing</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/import_last_tags_imported</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>import tags from XMP</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/import_last_directory</name>
    <type>dir</type>
    <default>$(home)</default>
    <shortdescription>last opened directory.</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/import_last_root</name>
    <type>string</type>
    <default/>
    <shortdescription>last root folder</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/import_last_folder_descending</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>folders display order in folder pane</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/import_initial_rating</name>
    <type min="0" max="5">int</type>
    <default>1</default>
    <shortdescription>initial rating</shortdescription>
    <longdescription>initial star rating for all images when importing a filmroll</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/ignore_exif_rating</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>ignore EXIF rating</shortdescription>
    <longdescription>ignore EXIF rating. if not set and EXIF rating is found, it overrides 'initial rating'</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/import_jobcode</name>
    <type>string</type>
    <default>no_name</default>
    <shortdescription>import job</shortdescription>
    <longdescription>name of the import job</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/import_datetime_override</name>
    <type>string</type>
    <default/>
    <shortdescription>override today's date</shortdescription>
    <longdescription>type a date in the form: YYYY:MM:DD[ hh:mm:ss[.sss]] if you want to override the current date/time used when expanding variables:\n$(YEAR), $(MONTH), $(DAY), $(HOUR), $(MINUTE), $(SECONDS), $(MSEC).\nlet the field empty otherwise</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>ui_last/import_keep_open</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>keep this window open</shortdescription>
    <longdescription>keep this window open to run several imports</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/capture/mode</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>capture view mode</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/overlay_timeout</name>
    <type>int</type>
    <default>3</default>
    <shortdescription>timeout for overlay hover block in seconds</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/overlays/0/0</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>overlays for filemanager at size 0 = none</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/overlays/0/1</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>overlays for filemanager at size 1 = on hover</shortdescription>
    <longdescription/>
  </dtconfig>
    <dtconfig>
    <name>plugins/lighttable/overlays/0/2</name>
    <type>int</type>
    <default>4</default>
    <shortdescription>overlays for filemanager at size 2 = always extended</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/overlays/1/0</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>overlays for filmstrip at size 0 = none</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/overlays/1/1</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>overlays for filmstrip at size 1 = on hover</shortdescription>
    <longdescription/>
  </dtconfig>
    <dtconfig>
    <name>plugins/lighttable/overlays/1/2</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>overlays for filmstrip at size 2 = on hover</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/overlays/2/0</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>overlays for zomable lighttable at size 0 = none</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/overlays/2/1</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>overlays for zomable lighttable at size 1 = on hover</shortdescription>
    <longdescription/>
  </dtconfig>
    <dtconfig>
    <name>plugins/lighttable/overlays/2/2</name>
    <type>int</type>
    <default>4</default>
    <shortdescription>overlays for zomable lighttable at size 2 = always extended</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/overlays/culling/0</name>
    <type>int</type>
    <default>6</default>
    <shortdescription>overlays for culling = block</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/overlays/culling/1</name>
    <type>int</type>
    <default>6</default>
    <shortdescription>overlays for full preview = block</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tooltips/0/0</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>tooltips for filemanager at size 0</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tooltips/0/1</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>tooltips for filemanager at size 1</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tooltips/0/2</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>tooltips for filemanager at size 2</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tooltips/1/0</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>tooltips for filmstrip at size 0</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tooltips/1/1</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>tooltips for filmstrip at size 1</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tooltips/1/2</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>tooltips for filmstrip at size 2</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tooltips/2/0</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>tooltips for zomable lighttable at size 0</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tooltips/2/1</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>tooltips for zomable lighttable at size 1</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tooltips/2/2</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>tooltips for zomable lighttable at size 2</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tooltips/culling/0</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>tooltips for culling</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tooltips/culling/1</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>tooltips for full preview</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/timeline/last_zoom</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>last timeline zoom.</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig prefs="lighttable" section="thumbs">
    <name>plugins/lighttable/thumbnail_raw_min_level</name>
   <type>
      <enum>
        <option>always</option>
        <option>small</option>
        <option>VGA</option>
        <option>720p</option>
        <option>1080p</option>
        <option>WQXGA</option>
        <option>4K</option>
        <option>5K</option>
        <option>never</option>
      </enum>
    </type>
    <default>never</default>
    <shortdescription>use raw file instead of embedded JPEG from size</shortdescription>
    <longdescription>if the thumbnail size is greater than this value, it will be processed using raw file instead of the embedded preview JPEG (better but slower).\nif you want all thumbnails and pre-rendered images in best quality you should choose the *always* option.\n(more comments in the manual)</longdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="thumbs">
    <name>plugins/lighttable/thumbnail_hq_min_level</name>
    <type>
      <enum>
        <option>always</option>
        <option>small</option>
        <option>VGA</option>
        <option>720p</option>
        <option>1080p</option>
        <option>WQXGA</option>
        <option>4K</option>
        <option>5K</option>
        <option>never</option>
      </enum>
    </type>
    <default>720p</default>
    <shortdescription>high quality processing from size</shortdescription>
    <longdescription>if the thumbnail size is greater than this value, it will be processed using the full quality rendering path (better but slower).\nif you want all thumbnails and pre-rendered images in best quality you should choose the *always* option.\n(more comments in the manual)</longdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="thumbs">
    <name>cache_disk_backend</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>enable disk backend for thumbnail cache</shortdescription>
    <longdescription>if enabled, write thumbnails to disk (.cache/darktable/) when evicted from the memory cache.\nnote that this can take a lot of memory (several gigabytes for 20k images) and will never delete cached thumbnails again.\nit's safe though to delete these manually, if you want.\nlight table performance will be increased greatly when browsing a lot.\nto generate all thumbnails of your entire collection offline, run 'darktable-generate-cache'.</longdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="thumbs">
    <name>cache_disk_backend_full</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>enable disk backend for full preview cache</shortdescription>
    <longdescription>if enabled, write full preview to disk (.cache/darktable/) when evicted from the memory cache.\nnote that this can take a lot of memory (several gigabytes for 20k images) and will never delete cached full previews again.\nit's safe though to delete these manually, if you want.\nlight table performance will be increased greatly when zooming image in full preview mode.</longdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="thumbs">
    <name>thumbtable_fractional_scrolling</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>enable smooth scrolling for lighttable thumbnails</shortdescription>
    <longdescription>if enabled, scrolling the lighttable scrolls by some number of pixels, as expected with a touch pad.\ndisabled, the lighttable scrolls full rows of thumbnails, as befits a scroll wheel.</longdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="thumbs">
    <name>backthumbs_mipsize</name>
    <type>
      <enum>
        <option>never</option>
        <option>small</option>
        <option>VGA</option>
        <option>720p</option>
        <option>1080p</option>
        <option>WQXGA</option>
        <option>4K</option>
        <option>5K</option>
      </enum>
    </type>
    <default>never</default>
    <shortdescription>generate thumbnails in background</shortdescription>
    <longdescription>if 'enable disk backend for thumbnail cache' is enabled thumbnails/mipmaps up to the selected size are generated while user is inactive in lighttable.</longdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="thumbs">
    <name>backthumbs_initialize</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>reset cached thumbnails</shortdescription>
    <longdescription>force thumbnails to be regenerated by resetting the database. this may be needed in case some thumbnails have been manually removed or corrupted.</longdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="thumbs">
    <name>plugins/lighttable/thumbnail_sizes</name>
    <type>string</type>
    <default>120|400</default>
    <shortdescription>delimiters for size categories</shortdescription>
    <longdescription>size categories are used to be able to set different overlays and CSS values depending of the size of the thumbnail, separated by |.\nfor example, 120|400 means 3 categories of thumbnails: &lt;120px, 120-400px, >400px</longdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="thumbs">
    <name>plugins/lighttable/extended_pattern</name>
    <type>longstring</type>
    <default>$(FILE_NAME).$(FILE_EXTENSION)$(NL)&lt;b&gt;$(EXIF.EXPOSURE)&lt;/b&gt; • &lt;b&gt;f/$(EXIF.APERTURE)&lt;/b&gt; • &lt;b&gt;$(EXIF.FOCAL.LENGTH)&lt;/b&gt;mm • ISO &lt;b&gt;$(EXIF.ISO)&lt;/b&gt; $(GPS.LOCATION.ICON) $(EXIF.FLASH.ICON) $(SIDECAR_TXT)</default>
    <shortdescription>pattern for the thumbnail extended overlay text</shortdescription>
    <longdescription>see manual to know all the tags you can use.</longdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="thumbs">
    <name>plugins/lighttable/thumbnail_tooltip_pattern</name>
    <type>longstring</type>
    <default>&lt;b&gt;$(FILE_NAME).$(FILE_EXTENSION)&lt;/b&gt;$(NL)$(EXIF.DATE.REGIONAL) $(EXIF.TIME.REGIONAL) $(GPS.LOCATION.ICON) $(EXIF.FLASH.ICON)$(NL)&lt;b&gt;$(EXIF.EXPOSURE)&lt;/b&gt; • &lt;b&gt;f/$(EXIF.APERTURE)&lt;/b&gt; • &lt;b&gt;$(EXIF.FOCAL.LENGTH)&lt;/b&gt;mm • ISO &lt;b&gt;$(EXIF.ISO)&lt;/b&gt;</default>
    <shortdescription>pattern for the thumbnail tooltip (empty to disable)</shortdescription>
    <longdescription>see manual to know all the tags you can use.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>pressure_sensitivity</name>
    <type>
      <enum>
        <option>off</option>
        <option>hardness (relative)</option>
        <option>hardness (absolute)</option>
        <option>opacity (relative)</option>
        <option>opacity (absolute)</option>
        <option>brush size (relative)</option>
      </enum>
    </type>
    <default>off</default>
    <shortdescription>pen pressure control for brush masks</shortdescription>
    <longdescription> - 'off': pressure reading ignored,\n - 'hardness'/'opacity'/'brush size': pressure reading controls specified attribute,\n - 'absolute'/'relative': pressure reading is taken directly as attribute value or multiplied with pre-defined setting.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>brush_smoothing</name>
    <type>
      <enum>
        <option>low</option>
        <option>medium</option>
        <option>high</option>
      </enum>
    </type>
    <default>medium</default>
    <shortdescription>smoothing of brush strokes</shortdescription>
    <longdescription>sets level for smoothing of brush strokes.\nstronger smoothing leads to less nodes and easier editing but with lower control of accuracy.</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="general">
    <name>masks_scroll_down_increases</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>scroll down to increase mask parameters</shortdescription>
    <longdescription>when using the mouse scroll wheel to change mask parameters, scroll down to increase the mask size, feather size, opacity, brush hardness and gradient curvature\nby default scrolling up increases these parameters</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="general">
    <name>darkroom/mouse/middle_button_cycle_zoom_to_200_percent</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>middle mouse button zooms to 200%</shortdescription>
    <longdescription>if enabled, the zoom level will cycle between 100%, 200% and fit to viewport on middle mouse clicks. if disabled, it will toggle between viewport size and 100%, and the 'ctrl' key can be used to control the zoom level.</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="modules">
    <name>channel_display</name>
    <type>
      <enum>
        <option>false color</option>
        <option>grayscale</option>
      </enum>
    </type>
    <default>false color</default>
    <shortdescription>display of individual color channels</shortdescription>
    <longdescription>defines how color channels are displayed when activated in the parametric masks feature.</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="general">
    <name>plugins/darkroom/image_infos_pattern</name>
    <type>longstring</type>
    <default>$(EXIF.EXPOSURE) • f/$(EXIF.APERTURE) • $(EXIF.FOCAL.LENGTH) mm • ISO $(EXIF.ISO) $(GPS.LOCATION.ICON) $(EXIF.FLASH.ICON)</default>
    <shortdescription>pattern for the image information line</shortdescription>
    <longdescription>see manual for a list of the tags you can use.</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="general">
    <name>plugins/darkroom/image_infos_position</name>
    <type>
      <enum>
        <option>top left</option>
        <option>top right</option>
        <option>top center</option>
        <option>bottom</option>
        <option>hidden</option>
      </enum>
    </type>
    <default>bottom</default>
    <shortdescription>position of the image information line</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>database_cache_quality</name>
    <type>int</type>
    <default>89</default>
    <shortdescription>JPEG quality of on-disk thumbnails</shortdescription>
    <longdescription>affects only the thumbnail cache used for quick startup.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/draw_group_borders</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>draw borders around grouped images</shortdescription>
    <longdescription>draw borders around grouped images when grouping is turned off and the mouse hovers over one of the images of the group</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="interface">
    <name>modules/default_presets_first</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>sort built-in presets first</shortdescription>
    <longdescription>whether to show built-in presets first before user's presets in presets menu.</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="modules">
    <name>plugins/darkroom/hide_default_presets</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>hide built-in presets for processing modules</shortdescription>
    <longdescription>hide built-in presets of processing modules in presets menu.</longdescription>
  </dtconfig>
    <dtconfig prefs="darkroom" section="modules">
    <name>plugins/darkroom/show_guides_in_ui</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>show the guides widget in modules UI</shortdescription>
    <longdescription>show the guides widget in modules UI</longdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="general">
    <name>plugins/lighttable/hide_default_presets</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>hide built-in presets for utility modules</shortdescription>
    <longdescription>hide built-in presets of utility modules in presets menu.</longdescription>
  </dtconfig>
  <dtconfig prefs="import" section="session">
    <name>session/base_directory_pattern</name>
    <type>string</type>
    <default>$(PICTURES_FOLDER)/Darktable</default>
    <shortdescription>base filmroll's directory</shortdescription>
    <longdescription>directory where new imported filmrolls are created</longdescription>
  </dtconfig>

  <dtconfig prefs="import" section="session">
    <name>session/sub_directory_pattern</name>
    <type>string</type>
    <default>$(YEAR)$(MONTH)$(DAY)_$(JOBCODE)</default>
    <shortdescription>filmroll name</shortdescription>
    <longdescription>name of the imported filmroll</longdescription>
  </dtconfig>

  <dtconfig prefs="import" section="session">
    <name>session/use_filename</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>keep original filename</shortdescription>
    <longdescription>keep original filename instead of a pattern while importing from camera or card</longdescription>
  </dtconfig>

  <dtconfig prefs="import" section="session">
    <name>session/filename_pattern</name>
    <type>string</type>
    <default>$(YEAR)$(MONTH)$(DAY)_$(SEQUENCE).$(FILE_EXTENSION)</default>
    <shortdescription>file naming pattern</shortdescription>
    <longdescription>file naming pattern used for a import session</longdescription>
  </dtconfig>

  <dtconfig>
    <name>plugins/lighttable/layout</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>lighttable layout mode</shortdescription>
    <longdescription>select a layout for the lighttable: 0 - zoomable lighttable or 1 - file manager or 2 - culling layout.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/base_layout</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>lighttable basic layout mode</shortdescription>
    <longdescription>the layout to return to when exiting culling layout</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/images_in_row</name>
    <type>int</type>
    <default>5</default>
    <shortdescription>images per row</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/culling_num_images</name>
    <type>int</type>
    <default>2</default>
    <shortdescription>images to display in culling layout</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/culling_zoom_mode</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>zoom mode in culling layout. 0=fixed ; 1=dynamic</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/culling_last_id</name>
    <type>int</type>
    <default>-1</default>
    <shortdescription>last culling first image id</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/recentcollect/num_items</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/recentcollect/line0</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/recentcollect/line1</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/recentcollect/line2</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/recentcollect/line3</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/recentcollect/line4</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/recentcollect/line5</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/recentcollect/line6</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/recentcollect/line7</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/recentcollect/line8</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/recentcollect/line9</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/copy_history/pastemode</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>append or replace history stack if pasted</shortdescription>
    <longdescription>0 -- append on top of stack, 1 -- replace it.</longdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="general">
    <name>plugins/lighttable/collect/single-click</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>use single-click in the collections module</shortdescription>
    <longdescription>check this option to use single-click to select items in the collections module. this will allow you to do range selections for date-time and numeric values.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/num_rules</name>
    <type min="1" max="10">int</type>
    <default>1</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/item0</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/string0</name>
    <type>string</type>
    <default>%</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/item1</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/string1</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/item2</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/string2</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/item3</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/string3</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/item4</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/string4</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/item5</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/string5</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/item6</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/string6</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/item7</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/string7</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/item8</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/string8</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/item9</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/string9</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/mode0</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/mode1</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/mode2</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/mode3</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/mode4</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/mode5</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/mode6</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/mode7</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/mode8</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/mode9</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/tagging/visible</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>show the tagging module in the darkroom view (as well as in lighttable)</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/export/visible</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>show the export module in the darkroom view (as well as in lighttable)</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig prefs="security" section="general">
    <name>plugins/lighttable/tagging/ask_before_delete_tag</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>ask before deleting a tag</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tagging/dttags</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>whether to show darktable internal tags</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tagging/nosuggestion</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>whether display suggestions</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tagging/treeview</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>whether display tags in list or tree</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tagging/hidehierarchy</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>whether to show/hide the tag hierarchy</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tagging/listsortedbycount</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>whether to sort tags by count or by name</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tagging/heightattachedwindow</name>
    <type>int</type>
    <default>100</default>
    <shortdescription>height of the tagging attached view</shortdescription>
    <longdescription>maximum height the tagging attached view will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tagging/heightdictionarywindow</name>
    <type>int</type>
    <default>200</default>
    <shortdescription>height of the tagging dictionary view</shortdescription>
    <longdescription>maximum height the tagging dictionary view will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig dialog="tagging">
    <name>plugins/lighttable/tagging/confidence</name>
    <type min="0" max="100">int</type>
    <default>50</default>
    <shortdescription>suggested tags level of confidence</shortdescription>
    <longdescription>level of confidence to include the tag in the suggestions list, 0: all associated tags, 99: 99% matching associated tags, 100: no matching tag to show only recent tags (faster)</longdescription>
  </dtconfig>
  <dtconfig dialog="tagging">
    <name>plugins/lighttable/tagging/nb_recent_tags</name>
    <type min="-1" max="1000">int</type>
    <default>20</default>
    <shortdescription>number of recently attached tags</shortdescription>
    <longdescription>number of recently attached tags which are included in the suggestions list. the value `-1' disables the recent list</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/tagging/recent_tags</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/metadata/creator_text_height</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>height of the metadata creator field</shortdescription>
    <longdescription>maximum height the metadata textview will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/metadata/publisher_text_height</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>height of the metadata publisher field</shortdescription>
    <longdescription>maximum height the metadata textview will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/metadata/title_text_height</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>height of the metadata title field</shortdescription>
    <longdescription>maximum height the metadata textview will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/metadata/description_text_height</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>height of the metadata description field</shortdescription>
    <longdescription>maximum height the metadata textview will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/metadata/rights_text_height</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>height of the metadata rights field</shortdescription>
    <longdescription>maximum height the metadata textview will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/metadata/notes_text_height</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>height of the metadata notes field</shortdescription>
    <longdescription>maximum height the metadata textview will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/metadata/version name_text_height</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>height of the metadata version name field</shortdescription>
    <longdescription>maximum height the metadata textview will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig prefs="security" section="general">
    <name>plugins/lighttable/style/ask_before_delete_style</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>ask before deleting a style</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig prefs="security" section="general">
    <name>plugins/lighttable/preset/ask_before_delete_preset</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>ask before deleting a preset</shortdescription>
    <longdescription>will ask for confirmation before deleting or overwriting a preset</longdescription>
  </dtconfig>
  <dtconfig prefs="security" section="general">
    <name>plugins/lighttable/export/ask_before_export_overwrite</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>ask before exporting in overwrite mode</shortdescription>
    <longdescription>will ask for confirmation before exporting files in overwrite mode</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>plugins/map/show_map_osd</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>show OSD</shortdescription>
    <longdescription>toggle the visibility of the map overlays</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>plugins/map/filter_images_drawn</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>filtered images</shortdescription>
    <longdescription>when set limit the images drawn to the current filmstrip</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>plugins/map/max_images_drawn</name>
    <type min="10" max="100000">int</type>
    <default>100</default>
    <shortdescription>max images</shortdescription>
    <longdescription>the maximum number of image thumbnails drawn on the map</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>plugins/map/epsilon_factor</name>
    <type min="1" max="100">int</type>
    <default>25</default>
    <shortdescription>group size factor</shortdescription>
    <longdescription>increase or decrease the spatial size of images groups on the map. can influence the calculation time</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>plugins/map/min_images_per_group</name>
    <type min="1" max="10">int</type>
    <default>1</default>
    <shortdescription>min images per group</shortdescription>
    <longdescription>the minimum number of images to set up an images group. can influence the calculation time.</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>plugins/map/images_thumbnail</name>
    <type>
      <enum>
        <option>thumbnail</option>
        <option>count</option>
        <option>none</option>
      </enum>
    </type>
    <default>thumbnail</default>
    <shortdescription>thumbnail display</shortdescription>
    <longdescription>three options are available: images thumbnails, only the count of images of the group or nothing</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/map/show_outline</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>Whether to highlight the search result on the map</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig ui="yes">
    <name>plugins/map/max_outline_nodes</name>
    <type>int</type>
    <default>10000</default>
    <shortdescription>max polygon points</shortdescription>
    <longdescription>limit the number of points imported with polygon in find location module</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="geoloc">
    <name>plugins/lighttable/metadata_view/pretty_location</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>pretty print the image location</shortdescription>
    <longdescription>show a more readable representation of the location in the image information module</longdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="general">
    <name>lighttable/ui/single_module</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>expand a single utility module at a time</shortdescription>
    <longdescription>this option toggles the behavior of shift clicking in lighttable mode</longdescription>
  </dtconfig>
  <dtconfig>
    <name>lighttable/ui/expose_statuses</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>always show thumbnail overlays</shortdescription>
    <longdescription>show overlays (rating stars, 'edited' mark, etc) for all thumbnails in file manager, not only hovered one</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="modules">
    <name>darkroom/ui/single_module</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>expand a single processing module at a time</shortdescription>
    <longdescription>this option toggles the behavior of shift clicking in darkroom mode</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="modules">
    <name>darkroom/ui/single_module_group_only</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>only collapse modules in current group</shortdescription>
    <longdescription>if only expanding a single module at a time, only collapse other modules in the current group - ignore modules in other groups</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="modules">
    <name>darkroom/ui/activate_expand</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>expand the module when it is activated, and collapse it when disabled</shortdescription>
    <longdescription>this option allows to expand or collapse automatically the module when it is enabled or disabled.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>darkroom/ui/overlay_contrast</name>
    <type>float</type>
    <default>0.5</default>
    <shortdescription>contrast to use in darkroom overlays</shortdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="general">
    <name>lighttable/ui/scroll_to_module</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>scroll utility modules to the top when expanded</shortdescription>
    <longdescription>when this option is enabled then darktable will try to scroll the module to the top of the visible list</longdescription>
  </dtconfig>
  <dtconfig>
    <name>lighttable/ui/preview/bottom_visible</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>show bottom panel in preview mode</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>lighttable/ui/preview/left_visible</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>show left panel in preview mode</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>lighttable/ui/preview/right_visible</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>show right panel in preview mode</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>lighttable/ui/preview/header_visible</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>show header panel in preview mode</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>lighttable/ui/preview/toolbar_bottom_visible</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>show toolbar bottom panel in preview mode</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>lighttable/ui/preview/toolbar_top_visible</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>show toolbar top panel in preview mode</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>lighttable/ui/preview/panels_collapse_controls</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>show borders arrows in preview mode</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>lighttable/ui/preview/panel_collaps_state</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>panels collapsing state in preview mode</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>slideshow/ui/panels_collapse_controls</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>show borders arrows in slideshow</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>slideshow/ui/panel_collaps_state</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>panels collapsing state in slideshow</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig prefs="darkroom" section="modules">
    <name>darkroom/ui/scroll_to_module</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>scroll processing modules to the top when expanded</shortdescription>
    <longdescription>when this option is enabled then darktable will try to scroll the module to the top of the visible list</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="interface">
    <name>darkroom/ui/sidebar_scroll_default</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>mouse wheel scrolls modules side panel by default</shortdescription>
    <longdescription>when enabled, use mouse wheel to scroll modules side panel.  use ctrl+alt to use mouse wheel for data entry.  when disabled, this behavior is reversed</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="general">
    <name>plugins/darkroom/ui/border_size</name>
    <type>int</type>
    <default>10</default>
    <shortdescription>border around image in darkroom mode</shortdescription>
    <longdescription>process the image in darkroom mode with a small border. set to 0 if you don't want any border.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>darkroom/ui/iso12464_border</name>
    <type min="1.0" max="5.0">float</type>
    <default>4.0</default>
    <shortdescription>total width of border in ISO 12646 mode (cm)</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>darkroom/ui/iso12464_ratio</name>
    <type min="0.1" max="0.9">float</type>
    <default>0.40</default>
    <shortdescription>fraction of white border part in ISO 12646 mode</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig prefs="darkroom" section="general">
    <name>darkroom/ui/scrollbars</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>show scrollbars for central view</shortdescription>
    <longdescription>defines whether scrollbars should be displayed</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="interface" restart="true">
    <name>panel_scrollbars_always_visible</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>always show panels' scrollbars</shortdescription>
    <longdescription>defines whether the panel scrollbars should be always visible or activated only depending on the content. (restart required)</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="interface">
    <name>darkroom/ui/transition_duration</name>
    <type min="0" max="1000">int</type>
    <default>250</default>
    <shortdescription>duration of the UI transitions in ms</shortdescription>
    <longdescription>how long the transitions take (in ms) for expanding or collapsing modules and other UI elements</longdescription>
  </dtconfig>
  <dtconfig>
    <name>ui/style/preview_size</name>
    <type min="100" max="500">int</type>
    <default>250</default>
    <shortdescription>max style preview size</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>ui_last/expander_metadata</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/expander_navigation</name>
    <type>int</type>
    <default>-1</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/expander_histogram</name>
    <type>int</type>
    <default>-1</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/expander_snapshots</name>
    <type>int</type>
    <default>-1</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/expander_history</name>
    <type>int</type>
    <default>-1</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig prefs="processing" section="general">
    <name>plugins/lighttable/export/force_lcms2</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>always use LittleCMS 2 to apply output color profile</shortdescription>
    <longdescription>this is slower than the default.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/export/high_quality_processing</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>do high quality resampling during export</shortdescription>
    <longdescription>the image will first be processed in full resolution, and downscaled at the very end. this can result in better quality sometimes, but will always be slower.</longdescription>
  </dtconfig>
 <dtconfig prefs="lighttable" section="general">
    <name>rating_one_double_tap</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>rating an image one star twice will not zero out the rating</shortdescription>
    <longdescription>defines whether rating an image one star twice will zero out star rating</longdescription>
  </dtconfig>
 <dtconfig prefs="lighttable" section="general">
    <name>lighttable/ui/scrollbars</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>show scrollbars for central view</shortdescription>
    <longdescription>defines whether scrollbars should be displayed</longdescription>
  </dtconfig>
  <dtconfig prefs="lighttable" section="general">
     <name>lighttable/ui/milliseconds</name>
     <type>bool</type>
     <default>false</default>
     <shortdescription>show image time with milliseconds</shortdescription>
     <longdescription>defines whether time should be displayed with milliseconds</longdescription>
   </dtconfig>
  <dtconfig>
    <name>darkroom/ui/rawoverexposed/mode</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>darkroom/ui/rawoverexposed/colorscheme</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>darkroom/ui/rawoverexposed/threshold</name>
    <type>float</type>
    <default>1.0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>darkroom/ui/overexposed/colorscheme</name>
    <type>int</type>
    <default>1</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>darkroom/ui/overexposed/lower</name>
    <type>float</type>
    <default>-12.69</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>darkroom/ui/overexposed/upper</name>
    <type>float</type>
    <default>99.99</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/channelmixerrgb/gui_page</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>last page selected in channel mixer rgb notebook</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/retouch/default_algo</name>
    <type>int</type>
    <default>2</default>
    <shortdescription>default algorithm for the retouch module</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/demosaic/fdc_xover_iso</name>
    <type>int</type>
    <default>1600</default>
    <shortdescription>crossover ISO for X-Trans FDC demosaicing</shortdescription>
    <longdescription>up to, and including, this ISO, X-Trans frequency domain chroma demosaicing uses the hybrid mode for determining chroma; for all higher ISO values the pure FDC is used.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/denoiseprofile/show_compute_variance_mode</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>whether to show the compute variance mode in denoiseprofile</shortdescription>
    <longdescription>adds a mode in denoiseprofile that allows to compute the variance after the generalized anscombe transform is performed</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="general" restart="true">
    <name>preview_downsampling</name>
    <type>
      <enum>
        <option>original</option>
        <option>to 1/2</option>
        <option>to 1/3</option>
        <option>to 1/4</option>
      </enum>
    </type>
    <default>original</default>
    <shortdescription>reduce resolution of preview image</shortdescription>
    <longdescription>decrease to speed up preview rendering, may hinder accurate masking</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="general">
    <name>darkroom/ui/loading_screen</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>show loading screen between images</shortdescription>
    <longdescription>show gray loading screen when navigating between images in the darkroom\ndisable to just show a toast message</longdescription>
  </dtconfig>
  <dtconfig>
    <name>darkroom/ui/develop_mask</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>show mask in developer mode</shortdescription>
    <longdescription>if switched on, the mask visualize button will show the 'pure mask' instead of mask and image content.</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="general">
    <name>plugins/lighttable/export/pixel_interpolator_warp</name>
    <type>
      <enum>
        <option>bilinear</option>
        <option>bicubic</option>
        <option>lanczos2</option>
      </enum>
    </type>
    <default>bicubic</default>
    <shortdescription>pixel interpolator (warp)</shortdescription>
    <longdescription>pixel interpolator used in modules for rotation, lens correction, liquify, cropping and final scaling (bilinear, bicubic, lanczos2).</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="general">
    <name>plugins/lighttable/export/pixel_interpolator</name>
    <type>
      <enum>
        <option>bilinear</option>
        <option>bicubic</option>
        <option>lanczos2</option>
        <option>lanczos3</option>
      </enum>
    </type>
    <default>lanczos3</default>
    <shortdescription>pixel interpolator (scaling)</shortdescription>
    <longdescription>pixel interpolator used for scaling (bilinear, bicubic, lanczos2, lanczos3).</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/export/dimensions_type</name>
    <type min="0">int</type>
    <default>0</default>
    <shortdescription>unit of the printing size</shortdescription>
    <longdescription>unit in which to input the image size.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/export/print_dpi</name>
    <type min="72" max="9600">int</type>
    <default>300</default>
    <shortdescription>DPI</shortdescription>
    <longdescription>print resolusion in DPI.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/export/width</name>
    <type min="0">int</type>
    <default>0</default>
    <shortdescription>width of the exported image</shortdescription>
    <longdescription>width of the exported image, or 0 if no scaling should be done.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/export/height</name>
    <type min="0">int</type>
    <default>0</default>
    <shortdescription>height of the exported image</shortdescription>
    <longdescription>height of the exported image, or 0 if no scaling should be done.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/export/storage_name</name>
    <type>string</type>
    <default>disk</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/export/format_name</name>
    <type>string</type>
    <default>jpeg</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/export/style</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/storage/disk/file_directory</name>
    <type>string</type>
    <default>$(FILE_FOLDER)/darktable_exported/$(FILE_NAME)</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/storage/gallery/file_directory</name>
    <type>string</type>
    <default>$(HOME)/darktable_gallery/img_$(SEQUENCE)</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/storage/gallery/title</name>
    <type>string</type>
    <default>darktable gallery</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>context_help/use_default_url</name>
    <type>boolean</type>
    <default>true</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>context_help/url</name>
    <type>string</type>
    <default>https://docs.darktable.org/usermanual/</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/storage/email/client</name>
    <type>string</type>
    <default/>
    <shortdescription>client to use, this overrides detection of default client.</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/storage/gphoto/id</name>
    <type>string</type>
    <default>642055548087-n01fgvugnbns7a9jq8jfucjsn5l1t6so.apps.googleusercontent.com</default>
    <shortdescription>google photo client id</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/storage/gphoto/secret</name>
    <type>string</type>
    <default>o29QcbsDWS5cauRqdmGdF3sP</default>
    <shortdescription>google photo client secret</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/pdf/size</name>
    <type>string</type>
    <default>a4</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/pdf/border</name>
    <type>string</type>
    <default>0 mm</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/pdf/dpi</name>
    <type>float</type>
    <default>300</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/pdf/bpp</name>
    <type>
      <enum>
        <option>8</option>
        <option>16</option>
      </enum>
    </type>
    <default>8</default>
    <shortdescription>PDF bit depth (bpp)</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/pdf/compression</name>
    <type>int</type>
    <default>1</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/exr/bpp</name>
    <type>
      <enum>
        <option>16</option>
        <option>32</option>
      </enum>
    </type>
    <default>32</default>
    <shortdescription>EXR float bit depth (bpp)</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/exr/compression</name>
    <type>int</type>
    <default>4</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/jpeg/quality</name>
    <type min="5" max="100">int</type>
    <default>95</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/j2k/quality</name>
    <type min="5" max="100">int</type>
    <default>95</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/tiff/bpp</name>
    <type>
      <enum>
        <option>8</option>
        <option>16</option>
        <option>32</option>
      </enum>
    </type>
    <default>8</default>
    <shortdescription>TIFF bit depth (bpp)</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/tiff/pixelformat</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/tiff/compress</name>
    <type min="0" max="2">int</type>
    <default>2</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/tiff/compresslevel</name>
    <type min="1" max="9">int</type>
    <default>6</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/tiff/shortfile</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/png/bpp</name>
    <type>
      <enum>
        <option>8</option>
        <option>16</option>
      </enum>
    </type>
    <default>8</default>
    <shortdescription>PNG bit depth (bpp)</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/png/compression</name>
    <type min="0" max="9">int</type>
    <default>5</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/jxl/bpp</name>
    <type>
      <enum>
        <option>8</option>
        <option>10</option>
        <option>12</option>
        <option>16</option>
        <option>32</option>
      </enum>
    </type>
    <default>8</default>
    <shortdescription>JPEG XL bit depth (bpp)</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/jxl/pixel_type</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/jxl/quality</name>
    <type min="4" max="100">int</type>
    <default>95</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/jxl/original</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/jxl/effort</name>
    <type min="1" max="9">int</type>
    <default>7</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/jxl/tier</name>
    <type min="0" max="4">int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/webp/comp_type</name>
    <type min="0" max="1">int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/webp/quality</name>
    <type min="0" max="100">int</type>
    <default>95</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/webp/hint</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/avif/bpp</name>
    <type>
      <enum>
        <option>8</option>
        <option>10</option>
        <option>12</option>
      </enum>
    </type>
    <default>8</default>
    <shortdescription>AVIF bit depth (bpp)</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/avif/color_mode</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/avif/tiling</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>AVIF Tiling</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/avif/compression_type</name>
    <type min="0" max="1">int</type>
    <default>1</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/avif/quality</name>
    <type min="0" max="100">int</type>
    <default>90</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/imageio/format/xcf/bpp</name>
    <type>
      <enum>
        <option>8</option>
        <option>16</option>
        <option>32</option>
      </enum>
    </type>
    <default>32</default>
    <shortdescription>XCF bit depth (bpp)</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig prefs="security" section="other" restart="true">
    <name>plugins/pwstorage/pwstorage_backend</name>
    <type>
      <enum>
        <option>auto</option>
        <option>none</option>
        <option capability="libsecret">libsecret</option>
        <option capability="kwallet">kwallet</option>
        <option capability="apple_keychain">apple_keychain</option>
        <option capability="windows_credentials">windows_credentials</option>
      </enum>
    </type>
    <default>auto</default>
    <shortdescription>password storage backend to use</shortdescription>
    <longdescription>the storage backend for password storage: auto, none, libsecret, kwallet, apple_keychain, windows_credentials</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/export/icctype</name>
    <type>int</type>
    <default>-1</default>
    <shortdescription>ICC profile type to use for export</shortdescription>
    <longdescription>this overrides the per-image settings, if not set to -1.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/export/iccprofile</name>
    <type>string</type>
    <default></default>
    <shortdescription>ICC profile filename to use for export</shortdescription>
    <longdescription>this overrides the per-image settings, if icctype not set to -1.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/export/iccintent</name>
    <type>int</type>
    <default>-1</default>
    <shortdescription>ICC rendering intent</shortdescription>
    <longdescription>if non-negative, this overrides the per-image output color profile rendering intent on export.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/groups</name>
    <type>int</type>
    <default>2</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/active</name>
    <type>string</type>
    <default/>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/spots/circle_size</name>
    <type>float</type>
    <default>0.02</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/spots/circle_border</name>
    <type>float</type>
    <default>0.02</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/spots/ellipse_radius_a</name>
    <type>float</type>
    <default>0.02</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/spots/ellipse_radius_b</name>
    <type>float</type>
    <default>0.01414</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/spots/ellipse_border</name>
    <type>float</type>
    <default>0.02</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/spots/ellipse_rotation</name>
    <type>float</type>
    <default>90.0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/spots/ellipse_flags</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/spots/path_border</name>
    <type>float</type>
    <default>0.05</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/spots/brush_border</name>
    <type>float</type>
    <default>0.05</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/spots/brush_density</name>
    <type>float</type>
    <default>1.0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/spots/brush_hardness</name>
    <type>float</type>
    <default>0.66</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/clipping/ratio_d</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>last chosen aspect ratio denominator</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/clipping/ratio_n</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>last chosen aspect ratio numerator</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/clipping/guide</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>last chosen guide style</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/graphheight</name>
    <type min="100" max="300">int</type>
    <default>180</default>
    <shortdescription>height of the scopes module</shortdescription>
    <longdescription>height of the scopes module in the darkroom and tethering views</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/colorequal/graphheight</name>
    <type min="100" max="300">int</type>
    <default>200</default>
    <shortdescription>height of the colorequal module</shortdescription>
    <longdescription>height of the colorequal module</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/navigation/graphheight</name>
    <type min="100" max="300">int</type>
    <default>200</default>
    <shortdescription>height of the navigation module</shortdescription>
    <longdescription>height of the navigation module in the darkroom</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/atrous/graphheight</name>
    <type min="100" max="300">int</type>
    <default>200</default>
    <shortdescription>height of contrast equalizer</shortdescription>
    <longdescription>height of contrast equalizer</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/denoiseprofile/graphheight</name>
    <type min="100" max="300">int</type>
    <default>200</default>
    <shortdescription>height of denoise (profiled) wavelets</shortdescription>
    <longdescription>height of denoise (profiled) wavelets</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/rawdenoise/graphheight</name>
    <type min="100" max="300">int</type>
    <default>200</default>
    <shortdescription>height of raw denoise wavelets</shortdescription>
    <longdescription>height of raw denoise wavelets</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/colorzones/graphheight</name>
    <type min="100" max="300">int</type>
    <default>200</default>
    <shortdescription>height of color zones graph in per cent</shortdescription>
    <longdescription>height of color zones graph in per cent</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/lowlight/graphheight</name>
    <type min="100" max="300">int</type>
    <default>200</default>
    <shortdescription>height of lowlight graph in per cent</shortdescription>
    <longdescription>height of lowlight graph in per cent</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/levels/graphheight</name>
    <type min="100" max="300">int</type>
    <default>200</default>
    <shortdescription>height of levels graph in per cent</shortdescription>
    <longdescription>height of levels graph in per cent</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/rgblevels/graphheight</name>
    <type min="100" max="300">int</type>
    <default>200</default>
    <shortdescription>height of rgb levels graph in per cent</shortdescription>
    <longdescription>height of rgb levels graph in per cent</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/filmicrgb/graphheight</name>
    <type min="100" max="300">int</type>
    <default>200</default>
    <shortdescription>height of filmic rgb graph in per cent</shortdescription>
    <longdescription>height of filmic rgb graph in per cent</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/colorbalancergb/graphheight</name>
    <type min="100" max="300">int</type>
    <default>200</default>
    <shortdescription>height of color balance rgb graph in per cent</shortdescription>
    <longdescription>height of color balance rgb graph in per cent</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/mode</name>
    <type>
      <enum>
        <option>vectorscope</option>
        <option>waveform</option>
        <option>RGB parade</option>
        <option>histogram</option>
      </enum>
    </type>
    <default>waveform</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig prefs="misc" section="interface" restart="true">
    <name>plugins/darkroom/histogram/panel_position</name>
    <type>
       <enum>
          <option>left</option>
          <option>right</option>
       </enum>
    </type>
    <default>right</default>
    <shortdescription>position of the scopes module</shortdescription>
    <longdescription>position the scopes at the top-left or top-right of the screen</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="modules">
    <name>plugins/darkroom/panel_swap</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>swap the utility and processing modules panels</shortdescription>
    <longdescription>move the list of processing modules to the left of the screen</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/histogram</name>
    <type>
      <enum>
        <option>logarithmic</option>
        <option>linear</option>
      </enum>
    </type>
    <default>logarithmic</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/orient</name>
    <type>
      <enum>
        <option>horizontal</option>
        <option>vertical</option>
      </enum>
    </type>
    <default>horizontal</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/waveform</name>
    <type>
      <enum>
        <option>overlaid</option>
        <option>parade</option>
      </enum>
    </type>
    <default>overlaid</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/vectorscope</name>
    <type>
      <enum>
        <option>u*v*</option>
        <option>AzBz</option>
        <option>RYB</option>
      </enum>
    </type>
    <default>u*v*</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/vectorscope/scale</name>
    <type>
      <enum>
        <option>logarithmic</option>
        <option>linear</option>
      </enum>
    </type>
    <default>logarithmic</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/vectorscope/angle</name>
    <type>int</type>
    <default>270</default>
    <shortdescription/>
    <longdescription>Angle in degrees to orient the vectorscope. 0 is the color science proper orientation (see CIE 1976 UCS diagram). 270 is what video editors are used to when using a vectorscope (with red/magenta in 12:00 position).</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/show_red</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/show_green</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/show_blue</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/vectorscope/harmony_type</name>
    <type>
      <enum>
		<option>none</option>
		<option>monochromatic</option>
		<option>analogous</option>
		<option>analogous complementary</option>
		<option>complementary</option>
		<option>split complementary</option>
		<option>dyad</option>
		<option>triad</option>
		<option>tetrad</option>
		<option>square</option>
      </enum>
    </type>
    <default>none</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/vectorscope/harmony_rotation</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/vectorscope/harmony_width</name>
    <type>
      <enum>
		<option>normal</option>
		<option>large</option>
		<option>narrow</option>
		<option>line</option>
      </enum>
    </type>
    <default>normal</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/histogram/vectorscope/harmony/dim</name>
    <type min="0" max="1">float</type>
    <default>0.7</default>
    <shortdescription>dim pixels outside of guides</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/opacity</name>
    <type>float</type>
    <default>1.0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/circle/size</name>
    <type>float</type>
    <default>0.05</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/circle/border</name>
    <type>float</type>
    <default>0.05</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/ellipse/radius_a</name>
    <type>float</type>
    <default>0.05</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/ellipse/radius_b</name>
    <type>float</type>
    <default>0.03535</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/ellipse/border</name>
    <type>float</type>
    <default>0.05</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/ellipse/rotation</name>
    <type>float</type>
    <default>90.0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/ellipse/flags</name>
    <type>int</type>
    <default>0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/path/border</name>
    <type>float</type>
    <default>0.05</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/gradient/rotation</name>
    <type>float</type>
    <default>0.0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/gradient/compression</name>
    <type>float</type>
    <default>0.5</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/gradient/steepness</name>
    <type>float</type>
    <default>0.0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/brush/border</name>
    <type>float</type>
    <default>0.05</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/brush/density</name>
    <type>float</type>
    <default>1.0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/brush/hardness</name>
    <type>float</type>
    <default>0.66</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/masks/heightview</name>
    <type min="100" max="1000">int</type>
    <default>300</default>
    <shortdescription>height of mask manager view window</shortdescription>
    <longdescription>maximum height the masks view in darkroom will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/ashift/near_delta</name>
    <type>float</type>
    <default>20.0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/ashift/near_delta_draw</name>
    <type>float</type>
    <default>5.0</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/print/print/black_point_compensation</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/print/print/grid_size</name>
    <type min="0" max="100">float</type>
    <default>10</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/print/print/unit</name>
    <type>
      <enum>
	<option>mm</option>
	<option>cm</option>
	<option>inch</option>
      </enum>
    </type>
    <default>mm</default>
    <shortdescription>measurement units</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/print/print/top_margin</name>
    <type min="-50" max="500">float</type>
    <default>17</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/print/print/bottom_margin</name>
    <type min="-50" max="500">float</type>
    <default>17</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/print/print/left_margin</name>
    <type min="-50" max="500">float</type>
    <default>17</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>plugins/print/print/right_margin</name>
    <type min="-50" max="500">float</type>
    <default>17</default>
    <shortdescription/>
    <longdescription/>
  </dtconfig>
  <dtconfig>
    <name>ui_last/theme</name>
    <type>string</type>
    <default>darktable-elegant-grey</default>
    <shortdescription>darktable theme</shortdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="interface">
    <name>ui_last/display_profile_source</name>
    <type>
      <enum>
        <option>all</option>
        <option>xatom</option>
        <option>colord</option>
      </enum>
    </type>
    <default>all</default>
    <shortdescription>method to use for getting the display profile</shortdescription>
    <longdescription>this option allows to force a specific means of getting the current display profile.\nthis is useful when one alternative gives wrong results</longdescription>
  </dtconfig>
  <dtconfig>
    <name>metadata/resolution</name>
    <type min="72" max="9600">int</type>
    <default>300</default>
    <shortdescription>DPI value for exported files</shortdescription>
    <longdescription/>
  </dtconfig>
  <dtconfig prefs="storage" section="XMP">
    <name>run_crawler_on_start</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>look for updated XMP files on startup</shortdescription>
    <longdescription>check file modification times of all XMP files on startup to check if any got updated in the meantime</longdescription>
  </dtconfig>
  <dtconfig>
    <name>colorlabel/red</name>
    <type>string</type>
    <default></default>
    <shortdescription>red color label</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>colorlabel/yellow</name>
    <type>string</type>
    <default></default>
    <shortdescription>yellow color label</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>colorlabel/green</name>
    <type>string</type>
    <default></default>
    <shortdescription>green color label</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>colorlabel/blue</name>
    <type>string</type>
    <default></default>
    <shortdescription>blue color label</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>colorlabel/purple</name>
    <type>string</type>
    <default></default>
    <shortdescription>purple color label</shortdescription>
  </dtconfig>
  <dtconfig prefs="security" section="other">
    <name>plugins/lighttable/audio_player</name>
    <type>string</type>
    <default>${DEFCONFIG_AUDIOPLAYER}</default>
    <shortdescription>executable for playing audio files</shortdescription>
    <longdescription>this external program is used to play audio files some cameras record to keep notes for images</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="general" restart="true">
    <name>plugins/darkroom/lut3d/def_path</name>
    <type>dir</type>
    <default>$(home)</default>
    <shortdescription>LUT 3D root folder</shortdescription>
    <longdescription>this folder (and sub-folders) contains LUT files used by LUT 3D module. (restart required)</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="general">
    <name>plugins/darkroom/workflow</name>
    <type>
      <enum>
        <option>scene-referred (filmic)</option>
        <option>scene-referred (sigmoid)</option>
        <option>display-referred (legacy)</option>
        <option>none</option>
      </enum>
    </type>
    <default>scene-referred (filmic)</default>
    <shortdescription>auto-apply pixel workflow defaults</shortdescription>
    <longdescription>scene-referred workflow is based on linear modules and will auto-apply filmic or sigmoid, color calibration and exposure,\ndisplay-referred workflow is based on Lab modules and will auto-apply base curve, white balance and the legacy module pipe order.</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="general">
    <name>plugins/darkroom/basecurve/auto_apply_percamera_presets</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>auto-apply per camera basecurve presets</shortdescription>
    <longdescription>use the per-camera basecurve by default instead of the generic manufacturer one if there is one available. (restart required)\nthis option is taken into account when the \"auto-apply pixel workflow defaults\" is set to \"display-referred\".\nto prevent auto-apply basecurve presets \"auto-apply pixel workflow defaults\" should be set to \"none\"</longdescription>
  </dtconfig>
  <dtconfig prefs="processing" section="general">
    <name>ui/detect_mono_exif</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>detect monochrome previews</shortdescription>
    <longdescription>many monochrome images can be identified via EXIF and preview data. beware: this slows down imports and reading of EXIF data</longdescription>
 </dtconfig>
 <dtconfig prefs="processing" section="general">
    <name>plugins/darkroom/show_warnings</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>show warning messages</shortdescription>
    <longdescription>display messages in modules to warn beginner users when non-standard and possibly harmful settings are used in the pipeline.\nthese messages can be false-positive and should be disregarded if you know what you are doing. this option will hide them all the time.</longdescription>
  </dtconfig>
 <dtconfig>
    <name>screen_dpi_overwrite</name>
    <type>float</type>
    <default>-1.0</default>
    <shortdescription>overwrite the screen's dpi setting</shortdescription>
    <longdescription>if this value is &gt; 0.0 then it is used as the screen's dpi setting which is used to scale the gui</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/preview/max_in_memory_images</name>
    <type>int</type>
    <default>4</default>
    <shortdescription>maximum number of full-res images to load in memory</shortdescription>
    <longdescription>if more images are display in expose mode, zooming will be deactivated</longdescription>
  </dtconfig>
  <dtconfig>
    <name>codepaths/openmp_simd</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>enable usage of OpenMP SIMD codepaths. if enabled, and such codepath exists, it will have the highest priority</shortdescription>
    <longdescription></longdescription>
  </dtconfig>
  <dtconfig>
    <name>allow_lab_output</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>allow XYZ and Lab color spaces as output</shortdescription>
    <longdescription>this is mainly useful for debugging and to create colorchecker LUT styles externally.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/rawprepare/allow_editing_crop</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>allow editing raw cropping boundaries</shortdescription>
    <longdescription>this is mainly useful for debugging and to add new camera support.</longdescription>
  </dtconfig>
  <dtconfig>
    <name>darkroom/modules/exposure/lightness</name>
    <type>float</type>
    <default>50.0</default>
    <shortdescription>lightness spot in exposure compensation</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>darkroom/modules/channelmixerrgb/colorchecker</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>current colorchecker model used</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>darkroom/modules/channelmixerrgb/optimization</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>current colorchecker optimization mode</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>darkroom/modules/channelmixerrgb/safety</name>
    <type>float</type>
    <default>0.5</default>
    <shortdescription>patch scale, safety margin</shortdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/modulegroups_preset</name>
    <type>string</type>
    <default></default>
    <shortdescription>preset use for defining iop layout</shortdescription>
    <longdescription>preset use for defining iop layout</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/modulegroups_basics_sections_labels</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>show the name of the module of each basic widget</shortdescription>
    <longdescription>show the name of the module of each basic widget</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/geotagging/heighttracklist</name>
    <type>int</type>
    <default>50</default>
    <shortdescription>height of the track list</shortdescription>
    <longdescription>maximum height the track list will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig ui="yes">
    <name>plugins/lighttable/geotagging/tz</name>
    <type>string</type>
    <default>UTC</default>
    <shortdescription>camera time zone</shortdescription>
    <longdescription>most cameras don't store the time zone in EXIF. give the correct time zone so the GPX data can be correctly matched</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/map/geotagging_search_url</name>
    <type>string</type>
    <default>https://nominatim.openstreetmap.org/search?q=%s&amp;format=xml&amp;limit=%d&amp;polygon_text=1</default>
    <shortdescription>Geotagging search URL</shortdescription>
    <longdescription>this can be changed when the default OpenStreetMap search URL is broken</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/temperature/colored_sliders</name>
    <type>
      <enum>
        <option>no color</option>
        <option>illuminant color</option>
        <option>effect emulation</option>
      </enum>
    </type>
    <default>no color</default>
    <shortdescription>white balance slider colors</shortdescription>
    <longdescription>visual indication of temperature adjustments.\nin 'illuminant color' mode slider colors represent the color of the light source,\nin 'effect emulation' slider colors represent the effect the adjustment would have on the scene</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/temperature/expand_coefficients</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>whether to show coefficients</shortdescription>
    <longdescription>this is just for remembering whether to show coefficients or not and remember it</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/temperature/button_bar</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>arange buttons as bar below temp/tint sliders</shortdescription>
    <longdescription>if enabled, the buttons in white balance module will be arranged below temp/tint sliders, otherwise they'll be shown in a grid to the right of temp/tint sliders</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/crop/expand_margins</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>whether to show margins</shortdescription>
    <longdescription>this is just for remembering whether to show margins or not and remember it</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/ashift/expand_values</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>whether to show values</shortdescription>
    <longdescription>this is just for remembering whether to show values or not</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/ashift/autocrop_value</name>
    <type>int</type>
    <default>1</default>
    <shortdescription>auto-crop mode</shortdescription>
    <longdescription>0=off ; 1= largest area ; 2= original image</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/colorzones/bg_sat_factor</name>
    <type min="0.1" max="1.0">float</type>
    <default>0.5</default>
    <shortdescription>saturation factor of the color zones background</shortdescription>
    <longdescription>higher value means more saturated backgrounds in color zones</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/colorbalance/layout</name>
    <type>
      <enum>
        <option>list</option>
        <option>tabs</option>
        <option>columns</option>
      </enum>
    </type>
    <default>list</default>
    <shortdescription>color balance slider block layout</shortdescription>
    <longdescription>choose how to organise the slider blocks for lift, gamma and gain:\n'list' - all sliders are shown in one long list (with headers),\n'tabs' - use tabs to switch between the blocks of sliders,\n'columns' - the blocks of sliders are shown next to each other (in narrow columns)</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="modules">
    <name>darkroom/ui/hide_header_buttons</name>
    <type>
      <enum>
        <option>always</option>
        <option>active</option>
        <option>dim</option>
        <option>auto</option>
        <option>fade</option>
        <option>fit</option>
        <option>smooth</option>
        <option>glide</option>
      </enum>
    </type>
    <default>always</default>
    <shortdescription>show right-side buttons in processing module headers</shortdescription>
    <longdescription>when the mouse is not over a module, the multi-instance, reset and preset buttons can be hidden:\n - 'always': always show all buttons,\n - 'active': only show the buttons when the mouse is over the module,\n - 'dim': buttons are dimmed when mouse is away,\n - 'auto': hide the buttons when the panel is narrow,\n - 'fade': fade out all buttons when panel narrows,\n - 'fit': hide all the buttons if the module name doesn't fit,\n - 'smooth': fade out all buttons in one header simultaneously,\n - 'glide': gradually hide individual buttons as needed</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="modules">
    <name>darkroom/ui/show_mask_indicator</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>show mask indicator in module headers</shortdescription>
    <longdescription>if enabled, an icon will be shown in the header of any processing modules that have a mask applied</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="modules">
    <name>darkroom/ui/rename_new_instance</name>
    <type>bool</type>
    <default>false</default>
    <shortdescription>prompt for name on addition of new instance</shortdescription>
    <longdescription>if enabled, a rename prompt will be present for each new module instance (either new instance or duplicate)</longdescription>
  </dtconfig>
  <dtconfig prefs="darkroom" section="modules">
    <name>darkroom/ui/auto_module_name_update</name>
    <type>bool</type>
    <default>true</default>
    <shortdescription>automatically update module name</shortdescription>
    <longdescription>if enabled, the module name will be automatically updated to match a preset name or a preset instance name if present.</longdescription>
  </dtconfig>

  @DARKTABLECONFIG_IOP_ENTRIES@

  <dtconfig>
    <name>darkroom/ui/scale_step_multiplier</name>
    <type>float</type>
    <default>1.0</default>
    <shortdescription>the multiplier that is applied to any slider value change</shortdescription>
    <longdescription>any slider value change will be multiplied by this number, when slider precision is set to normal</longdescription>
  </dtconfig>
  <dtconfig>
    <name>darkroom/ui/scale_rough_step_multiplier</name>
    <type>float</type>
    <default>10.0</default>
    <shortdescription>the multiplier that is applied to any slider rough value change</shortdescription>
    <longdescription>any slider value change will be multiplied by this number, when slider precision is set to coarse</longdescription>
  </dtconfig>
  <dtconfig>
    <name>darkroom/ui/scale_precise_step_multiplier</name>
    <type>float</type>
    <default>0.1</default>
    <shortdescription>the multiplier that is applied to any slider precise value change</shortdescription>
    <longdescription>any slider value change will be multiplied by this number, when slider precision is set to fine</longdescription>
  </dtconfig>
  <dtconfig>
    <name>darkroom/undo/merge_same_secs</name>
    <type>float</type>
    <default>10</default>
    <shortdescription>the interval within which same widget changes are merged for undo</shortdescription>
    <longdescription>when continuously manipulating the same widget (slider, curve) no new undo records are created during this time</longdescription>
  </dtconfig>
  <dtconfig>
    <name>darkroom/undo/review_secs</name>
    <type>float</type>
    <default>2</default>
    <shortdescription>create new undo record after reviewing last change to same widget this long</shortdescription>
    <longdescription>when continuing to change the same widget after a period of review allow undo to return to this state</longdescription>
  </dtconfig>
  <dtconfig>
    <name>performance_configuration_version_completed</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>version of the last completed performance configuration</shortdescription>
    <longdescription>what was the last performance configuration which has been completed</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/duplicate/windowheight</name>
    <type>int</type>
    <default>400</default>
    <shortdescription>height of the duplicates view</shortdescription>
    <longdescription>maximum height the duplicates view in darkroom will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/colorpicker/windowheight</name>
    <type>int</type>
    <default>200</default>
    <shortdescription>height of the color picker list</shortdescription>
    <longdescription>maximum height the color picker samples list in darkroom will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/history/windowheight</name>
    <type>int</type>
    <default>1000</default>
    <shortdescription>height of the history list</shortdescription>
    <longdescription>maximum height the history list in darkroom will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/metadata_view/windowheight</name>
    <type>int</type>
    <default>1000</default>
    <shortdescription>height of the image information view</shortdescription>
    <longdescription>maximum height the image information view in lighttable and darkroom will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/recentcollect/windowheight</name>
    <type>int</type>
    <default>1000</default>
    <shortdescription>height of the recent collections list</shortdescription>
    <longdescription>maximum height the recent collections list in lighttable will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/snapshots/windowheight</name>
    <type>int</type>
    <default>200</default>
    <shortdescription>height of the snapshots list</shortdescription>
    <longdescription>maximum height the snapshots list in darkroom will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/style/windowheight</name>
    <type>int</type>
    <default>400</default>
    <shortdescription>height of the styles list</shortdescription>
    <longdescription>maximum height the styles list in lighttable will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/darkroom/toneequal/gui_page</name>
    <type>int</type>
    <default>0</default>
    <shortdescription>active tab in tone equaliser module</shortdescription>
    <longdescription>which of simple, advanced or masking tabs will show at startup</longdescription>
  </dtconfig>
  <dtconfig>
    <name>plugins/lighttable/collect/windowheight</name>
    <type>int</type>
    <default>500</default>
    <shortdescription>height of the collect list</shortdescription>
    <longdescription>maximum height the collect list in lighttable will grow to before scrolling</longdescription>
  </dtconfig>
  <dtconfig prefs="misc" section="interface" capability="midi">
    <name>plugins/midi/devices</name>
    <type>string</type>
    <default></default>
    <shortdescription>order or exclude MIDI devices</shortdescription>
    <longdescription>comma-separated list of device name fragments that if matched load MIDI device at id given by location in list\nor if preceded by '-' prevent matching devices from loading. add encoding and number of knobs like 'BeatStep:63:16'</longdescription>
  </dtconfig>
</dtconfiglist>

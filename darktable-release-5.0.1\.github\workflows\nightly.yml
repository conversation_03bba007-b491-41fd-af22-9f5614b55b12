name: Nightly PKG

on:
  schedule:
    - cron: "0 0 * * *"
  workflow_dispatch:

permissions:
  contents: read
  actions: write  # We need this to be able to cancel workflow if job fails

jobs:
  AppImage:
    if: github.repository == 'darktable-org/darktable' || github.event_name == 'workflow_dispatch'
    name: Nightly darktable AppImage
    runs-on: ubuntu-22.04
    strategy:
      fail-fast: false
      matrix:
        compiler:
          - { compiler: GNU12, CC: gcc-12, CXX: g++-12, packages: gcc-12 g++-12 }
        branch:
          - { code: master, label: gitmaster }
    env:
      CC: ${{ matrix.compiler.CC }}
      CXX: ${{ matrix.compiler.CXX }}
      SRC_DIR: ${{ github.workspace }}/src
      BUILD_DIR: ${{ github.workspace }}/build
      INSTALL_PREFIX: ${{ github.workspace }}/AppDir/usr
      CMAKE_BUILD_TYPE: ${{ matrix.btype }}
      GENERATOR: ${{ matrix.generator }}
      TARGET: ${{ matrix.target }}
      DARKTABLE_CLI: ${{ github.workspace }}/AppDir/usr/bin/darktable-cli
      BRANCH: ${{ matrix.branch.code }}
      BUILD_NAME: ${{ matrix.branch.label }}
    steps:
      - name: Install compiler ${{ matrix.compiler.compiler }}
        run: |
          sudo apt-get update
          sudo apt-get -y install \
            ${{ matrix.compiler.packages }}
      - name: Install Base Dependencies
        run: |
          sudo apt-get -y install \
            build-essential \
            appstream-util \
            desktop-file-utils \
            gettext \
            git \
            gdb \
            intltool \
            libatk1.0-dev \
            libcairo2-dev \
            libcolord-dev \
            libcolord-gtk-dev \
            libcups2-dev \
            libcurl4-gnutls-dev \
            libimage-exiftool-perl \
            libfuse2 \
            libgdk-pixbuf2.0-dev \
            libglib2.0-dev \
            libgmic-dev \
            libgraphicsmagick1-dev \
            libgtk-3-dev \
            libinih-dev \
            libjson-glib-dev \
            liblcms2-dev \
            liblensfun-dev \
            liblensfun-bin \
            liblensfun-data-v1 \
            liblensfun1 \
            libosmgpsmap-1.0-dev \
            libpango1.0-dev \
            libpng-dev \
            libportmidi-dev \
            libpugixml-dev \
            librsvg2-dev \
            libsaxon-java \
            libsdl2-dev \
            libsecret-1-dev \
            libsqlite3-dev \
            libtiff5-dev \
            libwebp-dev \
            libx11-dev \
            libxml2-dev \
            libxml2-utils \
            ninja-build \
            perl \
            po4a \
            python3-jsonschema \
            xsltproc \
            zlib1g-dev \
            appstream;
          sudo add-apt-repository -y ppa:savoury1/graphics
          sudo add-apt-repository -y ppa:savoury1/ffmpeg4
          sudo add-apt-repository -y ppa:savoury1/display
          sudo apt-get update
          sudo apt-get -y install \
            libavif-dev \
            libgphoto2-dev \
            libheif-dev \
            libimath-dev \
            libjpeg-turbo8-dev \
            libjxl-dev \
            libopenexr-dev \
            libopenjp2-7-dev \
            x11proto-dev \
            libxfixes-dev;
      - name: Build and install a more recent version of exiv2
        run: |
          git clone --branch v0.28.3 --depth 1 https://github.com/Exiv2/exiv2 src-exiv2
          cd src-exiv2
          cmake -S . -B build -G Ninja \
            -DCMAKE_BUILD_TYPE=Release \
            -DBUILD_SHARED_LIBS=ON \
            -DEXIV2_ENABLE_NLS=ON \
            -DEXIV2_ENABLE_VIDEO=OFF \
            -DCMAKE_INSTALL_PREFIX=/usr
          cmake --build build
          sudo cmake --install build
          cd ..
      - name: Cancel workflow if job fails
        if: ${{ failure() }}
        uses: andymckay/cancel-action@0.5
      - name: Checkout darktable master branch
        run: |
          # Note that we can't make a shallow clone to reduce clone traffic and time, as we have to
          # fetch the entire history to correctly generate the version for the AppImage filename
          git clone https://github.com/darktable-org/darktable src
          pushd src
          git submodule init
          git config submodule.src/tests/integration.update none
          git submodule update
          popd
      - name: Update lensfun data
        continue-on-error: true
        run: |
          sudo lensfun-update-data
      - name: Build and Install
        run: |
          cd src
          bash tools/appimage-build-script.sh
      - name: Package upload
        if: ${{ success() }}
        uses: actions/upload-artifact@v4
        with:
          path: ${{ github.workspace }}/src/build/Darktable-*.AppImage*
          name: artifact-appimage
          retention-days: 1

  Windows:
    if: github.repository == 'darktable-org/darktable' || github.event_name == 'workflow_dispatch'
    name: Nightly darktable Windows
    runs-on: windows-latest
    strategy:
      fail-fast: true
      matrix:
        btype:
          - Release
        msystem:
          - UCRT64
        target:
          - skiptest
        generator:
          - Ninja
        eco: [-DBINARY_PACKAGE_BUILD=ON]
    defaults:
      run:
        shell: msys2 {0}
    env:
      SRC_DIR: ${{ github.workspace }}/src
      BUILD_DIR: ${{ github.workspace }}/build
      INSTALL_PREFIX: ${{ github.workspace }}/install
      ECO: ${{ matrix.eco }}
      CMAKE_BUILD_TYPE: ${{ matrix.btype }}
      TARGET: ${{ matrix.target }}
      GENERATOR: ${{ matrix.generator }}
    steps:
      - uses: msys2/setup-msys2@v2
        with:
          msystem: ${{ matrix.msystem }}
          install: >-
            git
            intltool
            po4a
          pacboy: >-
            cc:p
            cmake:p
            libxslt:p
            ninja:p
            nsis:p
            python-jsonschema:p
            curl:p
            drmingw:p
            gcc-libs:p
            gettext:p
            gmic:p
            graphicsmagick:p
            gtk3:p
            icu:p
            imath:p
            iso-codes:p
            lcms2:p
            lensfun:p
            libavif:p
            libgphoto2:p
            libheif:p
            libjpeg-turbo:p
            libjxl:p
            libpng:p
            librsvg:p
            libsecret:p
            libtiff:p
            libwebp:p
            libxml2:p
            lua:p
            omp:p
            openexr:p
            openjpeg2:p
            osm-gps-map:p
            portmidi:p
            pugixml:p
            SDL2:p
            sqlite3:p
            webp-pixbuf-loader:p
            zlib:p
          update: false
      - name: Install the latest not yet broken version of exiv2
        run: |
          # It's a temporary fix, to be removed when the issue will be resolved
          pacman -U --noconfirm https://repo.msys2.org/mingw/ucrt64/mingw-w64-ucrt-x86_64-exiv2-0.27.7-1-any.pkg.tar.zst
      - name: Cancel workflow if job fails
        if: ${{ failure() }}
        uses: andymckay/cancel-action@0.5
      - run: git config --global core.autocrlf input
        shell: bash
      - name: Checkout darktable master branch
        run: |
          # Note that we can't make a shallow clone to reduce clone traffic and time, as we have to
          # fetch the entire history to correctly generate the version for the installation package filename
          git clone https://github.com/darktable-org/darktable src
          pushd src
          git submodule init
          git config submodule.src/tests/integration.update none
          git submodule update
          popd
      - name: Update lensfun data
        if: ${{ success() && matrix.btype == 'Release' && matrix.target == 'skiptest' }}
        continue-on-error: true
        run: |
          lensfun-update-data
      - name: Build and Install
        run: |
          cmake -E make_directory "${BUILD_DIR}"
          cmake -E make_directory "${INSTALL_PREFIX}"
          $(cygpath ${SRC_DIR})/.ci/ci-script.sh
      - name: Package
        if: ${{ success() && matrix.btype == 'Release' && matrix.target == 'skiptest' }}
        run: |
          cd "${BUILD_DIR}"
          cmake --build "${BUILD_DIR}" --target package
      - name: Get version info
        run: |
          cd ${SRC_DIR}
          echo "VERSION=$(git describe --tags --match release-* | sed 's/^release-//;s/-/+/;s/-/~/;s/rc/~rc/')" >> $GITHUB_ENV
          ([[ ${MSYSTEM_CARCH} == x86_64 ]] && echo "SYSTEM=win64" || echo "SYSTEM=woa64") >> $GITHUB_ENV
      - name: Package upload
        if: ${{ success() }}
        uses: actions/upload-artifact@v4
        with:
          path: ${{ env.BUILD_DIR }}/darktable-${{ env.VERSION }}-${{ env.SYSTEM }}.exe
          name: artifact-windows
          retention-days: 2

  macOS:
    if: github.repository == 'darktable-org/darktable' || github.event_name == 'workflow_dispatch'
    name: Nightly darktable macOS
    runs-on: ${{ matrix.build.os }}
    strategy:
      fail-fast: true
      matrix:
        build:
          - { os: macos-13, xcode: 15.2, deployment: 13.5 } # LLVM16, x86_64
          - { os: macos-14, xcode: 15.4, deployment: 14.0 } # LLVM16, arm64
        compiler:
          - { compiler: XCode,   CC: cc, CXX: c++ }
        btype: [ Release ]
        eco: [-DBINARY_PACKAGE_BUILD=ON -DBUILD_CURVE_TOOLS=ON -DBUILD_NOISE_TOOLS=ON -DUSE_GRAPHICSMAGICK=OFF -DUSE_IMAGEMAGICK=ON]
        target:
          - skiptest
        generator:
          - Ninja
    env:
      DEVELOPER_DIR: /Applications/Xcode_${{ matrix.build.xcode }}.app/Contents/Developer
      CC: ${{ matrix.compiler.CC }}
      CXX: ${{ matrix.compiler.CXX }}
      MACOSX_DEPLOYMENT_TARGET: ${{ matrix.build.deployment }}
      SRC_DIR: ${{ github.workspace }}/src
      BUILD_DIR: ${{ github.workspace }}/src/build
      INSTALL_PREFIX: ${{ github.workspace }}/src/build/macosx
      ECO: ${{ matrix.eco }}
      CMAKE_BUILD_TYPE: ${{ matrix.btype }}
      GENERATOR: ${{ matrix.generator }}
      TARGET: ${{ matrix.target }}
    steps:
      - name: Checkout darktable master branch
        run: |
          # Note that we can't make a shallow clone to reduce clone traffic and time, as we have to
          # fetch the entire history to correctly generate the version for the disk image filename
          git clone https://github.com/darktable-org/darktable src
          pushd src
          git submodule init
          git config submodule.src/tests/integration.update none
          git submodule update
          popd
      - name: Install Base Dependencies
        run: |
          brew update > /dev/null || true
          # brew upgrade || true        # No need for a very time-consuming upgrade of ALL packages
          brew install --force gd       # See https://github.com/Homebrew/homebrew-core/issues/141766
          brew tap Homebrew/bundle
          cd src/.ci
          export HOMEBREW_NO_INSTALL_UPGRADE=1
          brew bundle --verbose || true
          # handle keg-only libs
          brew link --force libomp
          brew link --force libsoup@2
      - name: Cancel workflow if job fails
        if: ${{ failure() }}
        uses: andymckay/cancel-action@0.5
      - name: Build and Install
        run: |
          cmake -E make_directory "${BUILD_DIR}";
          cmake -E make_directory "${INSTALL_PREFIX}";
          ./src/.ci/ci-script.sh;
      - name: Build macOS package
        run: |
          ./src/packaging/macosx/3_make_hb_darktable_package.sh
      - name: Create DMG file
        run: |
          ./src/packaging/macosx/4_make_hb_darktable_dmg.sh
      - name: Get architecture
        run: |
          echo "ARCHITECTURE=$(uname -m)" >> $GITHUB_ENV
      - name: Get version info
        run: |
          cd ${{ env.SRC_DIR }}
          echo "VERSION=$(git describe --tags --match release-* | sed 's/^release-//;s/-/+/;s/-/~/;s/rc/~rc/')-${{ env.ARCHITECTURE }}" >> $GITHUB_ENV
      - name: Package upload
        if: ${{ success() }}
        uses: actions/upload-artifact@v4
        with:
          path: ${{ env.INSTALL_PREFIX }}/darktable-${{ env.VERSION }}.dmg
          name: artifact-macos-${{ env.ARCHITECTURE }}
          retention-days: 2

  upload_to_release:
    permissions:
      # We need write permission to update the nightly tag
      contents: write
    runs-on: ubuntu-latest
    needs: [AppImage, Windows, macOS]
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4
      - name: Update nightly release
        uses: andelf/nightly-release@main
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: nightly
          prerelease: true
          name: 'Darktable nightly build $$'
          # Please do not modify the body text by adding line breaks in paragraphs, as this text should display well on screens of different widths
          body: |
            This is a nightly build of Darktable.

            You can use this if you want to try new features without waiting for releases. From time to time, in development builds, old difficult-to-reproduce bugs are fixed, but it is also true that in the development process with the introduction of new complex code, the stability of the program may suffer compared to official releases, so **use it with caution**!

            Also, new versions can make changes to the database schema, so it's best to run them with a separate library.

            The AppImage package is compatible with distribution releases that have glibc version 2.35 or higher. For example, if we consider some popular distributions, Ubuntu 22.04, Debian 12, Fedora 36 and newer releases are compatible.

            The `*.AppImage.zsync` file is not intended to be downloaded and used locally. Just ignore it. This file contains technical information required by AppImage auto-updaters such as [AppImageUpdate](https://appimage.github.io/AppImageUpdate/).

            The Windows package requires Windows with UCRT (Universal C Runtime), which is shipped with Windows 10+. Darktable should also work on Windows 8.1 [on condition that you install this runtime yourself](https://support.microsoft.com/en-us/topic/update-for-universal-c-runtime-in-windows-c0514201-7fe6-95a3-b0a5-287930f3560c).

            The macOS `*-x86_64.dmg` package requires at least macOS 13.5 (Ventura), the `*-arm64.dmg` package requires at least macOS 14.0 (Sonoma).

            __Please help us improve Darktable by reporting any issues you encounter!__ :wink:
          files: |
            artifact-appimage/*
            artifact-windows/*
            artifact-macos-*/*

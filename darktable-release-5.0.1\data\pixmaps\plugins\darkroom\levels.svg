<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="200"
   height="200"
   id="svg2"
   sodipodi:version="0.32"
   inkscape:version="0.48.0 r9654"
   version="1.0"
   sodipodi:docname="levels.svg"
   inkscape:output_extension="org.inkscape.output.svg.inkscape"
   inkscape:export-filename="/home/<USER>/projects/darktable/data/pixmaps/plugins/darkroom/levels.png"
   inkscape:export-xdpi="10.8"
   inkscape:export-ydpi="10.8">
  <defs
     id="defs4">
    <inkscape:perspective
       sodipodi:type="inkscape:persp3d"
       inkscape:vp_x="0 : 526.18109 : 1"
       inkscape:vp_y="0 : 1000 : 0"
       inkscape:vp_z="744.09448 : 526.18109 : 1"
       inkscape:persp3d-origin="372.04724 : 350.78739 : 1"
       id="perspective10" />
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     gridtolerance="10000"
     guidetolerance="10"
     objecttolerance="10"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="2.0225"
     inkscape:cx="138.30959"
     inkscape:cy="68.020005"
     inkscape:document-units="px"
     inkscape:current-layer="layer1"
     showgrid="true"
     units="px"
     inkscape:window-width="1280"
     inkscape:window-height="946"
     inkscape:window-x="0"
     inkscape:window-y="24"
     inkscape:window-maximized="1"
     inkscape:snap-nodes="true"
     inkscape:object-nodes="true">
    <inkscape:grid
       type="xygrid"
       id="grid2383"
       visible="true"
       enabled="true" />
  </sodipodi:namedview>
  <metadata
     id="metadata7">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Layer 1"
     inkscape:groupmode="layer"
     id="layer1">
    <path
       style="fill:#000000;stroke:#000000;stroke-width:1px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;fill-opacity:1"
       d="m 34,116 c 0,0 3,-7 3,-9 0,-2 2,2 3,1 1,-1 3,-2 3,-6 0,-4 3,-23 4,-24 1,-1 2,-13 3,-12 1,1 3,12 3,14 0,2 2,10 3,11 1,1 5,5 8,5 3,0 8,-2 10,-1 2,1 7,-3 8,-2 1,1 2,5 4,4 2,-1 8,-4 10,-3 2,1 2,5 5,3 3,-2 7,-6 9,-4 2,2 3,-7 6,-5 3,2 8,5 12,5 4,0 13,-13 15,-11 2,2 4,-4 5,-2 1,2 1,20 3,19 2,-1 3,6 4,5 1,-1 2,-4 3,-3 1,1 0,6 2,5 2,-1 3,-2 4,-1 1,1 2,5 2,5 l -1,15 -15,20 -20,20 -30,5 -30,-5 -30,-25 z"
       id="path5343"
       inkscape:connector-curvature="0" />
    <path
       sodipodi:type="arc"
       style="fill:none;fill-rule:evenodd;stroke:#cacac6;stroke-width:10;stroke-linecap:round;stroke-linejoin:round;stroke-opacity:1;stroke-miterlimit:4;stroke-dasharray:none"
       id="path2385"
       sodipodi:cx="147.5"
       sodipodi:cy="87.5"
       sodipodi:rx="70"
       sodipodi:ry="70"
       d="M 217.5,87.5 A 70,70 0 1 1 77.5,87.5 A 70,70 0 1 1 217.5,87.5 z"
       transform="translate(-47.5,12.5)" />
    <path
       sodipodi:type="star"
       style="color:#000000;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:#cacac6;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate"
       id="path3780"
       sodipodi:sides="3"
       sodipodi:cx="95"
       sodipodi:cy="80"
       sodipodi:r1="20"
       sodipodi:r2="10"
       sodipodi:arg1="-1.5707963"
       sodipodi:arg2="-0.52359878"
       inkscape:flatsided="false"
       inkscape:rounded="0"
       inkscape:randomized="0"
       d="m 95.000001,60 8.660249,15 8.66026,15 L 95,90 77.679492,90 86.339746,75 z"
       inkscape:transform-center-y="-5"
       transform="translate(-35,85)" />
    <path
       sodipodi:type="star"
       style="color:#000000;fill:#808080;fill-opacity:0.98823529;fill-rule:nonzero;stroke:#cacac6;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate"
       id="path3780-0"
       sodipodi:sides="3"
       sodipodi:cx="95"
       sodipodi:cy="80"
       sodipodi:r1="20"
       sodipodi:r2="10"
       sodipodi:arg1="-1.5707963"
       sodipodi:arg2="-0.52359878"
       inkscape:flatsided="false"
       inkscape:rounded="0"
       inkscape:randomized="0"
       d="m 95.000001,60 8.660249,15 8.66026,15 L 95,90 77.679492,90 86.339746,75 z"
       inkscape:transform-center-y="-5"
       transform="translate(6.339746,85)" />
    <path
       sodipodi:type="star"
       style="color:#000000;fill:#ffffff;fill-opacity:0.98920076999999995;fill-rule:nonzero;stroke:#cacac6;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate"
       id="path3780-0-2"
       sodipodi:sides="3"
       sodipodi:cx="95"
       sodipodi:cy="80"
       sodipodi:r1="20"
       sodipodi:r2="10"
       sodipodi:arg1="-1.5707963"
       sodipodi:arg2="-0.52359878"
       inkscape:flatsided="false"
       inkscape:rounded="0"
       inkscape:randomized="0"
       d="m 95.000001,60 8.660249,15 8.66026,15 L 95,90 77.679492,90 86.339746,75 z"
       inkscape:transform-center-y="-5"
       transform="translate(50,85)" />
  </g>
</svg>

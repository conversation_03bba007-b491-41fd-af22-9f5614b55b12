/*
    This file is part of darktable,
    copyright (c) 2021 <PERSON>

    darktable is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    darktable is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    ME<PERSON><PERSON><PERSON><PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with darktable.  If not, see <http://www.gnu.org/licenses/>.
*/

/* This has been tested with GTK 3.24 on Gnome */

@import url("darktable-elegant-dark.css");

/* Perceptually uniform grey gradient */

/* General */
@define-color selected_bg_color @grey_55;       /* legacy stuff */
@define-color border_color @grey_40;            /* border, when used */
@define-color bg_color @grey_45;                /* general background */
@define-color fg_color @grey_95;                /* general text */
@define-color text_color @grey_35;              /* same */
@define-color placeholder_text_color @grey_70;  /* placeholder text color (text on search background fields) */
@define-color disabled_fg_color @grey_65;       /* disabled controls */

/* Scroll bars (sliders) */
@define-color scroll_bar_inactive @grey_60;
@define-color scroll_bar_active @grey_80;
@define-color scroll_bar_bg @grey_40;

/* Modules box (plugins) */
@define-color plugin_bg_color shade(@darkroom_bg_color, 0.95);
@define-color plugin_label_color @grey_80;
@define-color collapsible_bg_color @grey_55;

/* Modules controls (sliders and comboboxes) */
@define-color bauhaus_fg shade(@fg_color, 0.95);   /* needed to show color picker on bauhaus sliders */
@define-color bauhaus_indicator_border @grey_50;
@define-color bauhaus_fill @grey_70;
@define-color bauhaus_bg_hover @grey_80;
@define-color bauhaus_fg_hover @grey_100;
@define-color bauhaus_fg_selected @grey_75;
@define-color bauhaus_fg_insensitive alpha(@bauhaus_fg, 0.5);

/* GTK Buttons and tabs */
@define-color button_bg shade(@grey_55,0.95);
@define-color button_hover_bg @grey_70;
@define-color button_hover_fg @grey_30;

/* text fields */
@define-color field_bg @grey_45;
@define-color field_active_bg @grey_50;
@define-color field_active_fg @grey_95;
@define-color field_selected_bg @grey_60;
@define-color field_selected_fg @grey_100;
@define-color field_hover_bg @grey_75;
@define-color field_hover_fg @grey_35;

/* Tooltips and contextual helpers */
@define-color tooltip_bg_color @grey_35;
@define-color tooltip_fg_color @grey_80;
@define-color log_fg_color @grey_95;

/* Views */
@define-color lighttable_bg_color @grey_60;
@define-color lighttable_bg_font_color @grey_95;

/* Lighttable and film-strip */
@define-color thumbnail_font_color @grey_40;
@define-color thumbnail_bg_color @grey_70;
@define-color thumbnail_fg_color @grey_60;
@define-color thumbnail_selected_bg_color @grey_80;
@define-color thumbnail_hover_bg_color @grey_95;
@define-color thumbnail_hover_fg_color @grey_100;
@define-color thumbnail_localcopy_color @grey_90;

@define-color range_bg_color alpha(@fg_color, 0.05);

/* Graphs : histogram, navigation thumbnail and some items on tone curve */
@define-color graph_bg @grey_40;
@define-color graph_border @grey_15;
@define-color graph_fg @grey_100;
@define-color graph_grid @grey_30;
@define-color inset_histogram alpha(@grey_95, 0.50);

/* Adjust middle grey picker on levels and rgb levels modules to make it visible */
#picker-grey
{
  color: @grey_70;
}

/* hover effect on combo and bauhaus */
.combo:hover,
.combo:hover cellview,
.dt_bauhaus:hover,
combobox window *:hover
{
  color: shade(@fg_color, 0.94);
}

/* set default text, items that can be selected : items inside categories titles set above */
.dt_bauhaus_popup
{
  color: shade(@fg_color, 0.75);
}

/* Set background on thumbnails hover overlays */
.dt_overlays_hover_extended #thumb-main:hover #thumb-bottom,
.dt_overlays_mixed #thumb-main:hover #thumb-bottom,
.dt_overlays_hover #thumb-main:hover #thumb-bottom
{
  background-image: linear-gradient(rgba(241, 241, 241, 0.7) 0%, rgba(241, 241, 241, 0.7) 90%,rgba(241, 241, 241, 0) 100%);  /* rgb color needs to be set to same color as #thumb_back hover background */
}

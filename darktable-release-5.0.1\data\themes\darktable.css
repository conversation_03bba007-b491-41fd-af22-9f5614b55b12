/*
    This file is part of darktable,
    copyright (c) 2022 darktable developers.

    darktable is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    darktable is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    ME<PERSON><PERSON><PERSON><PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with darktable.  If not, see <http://www.gnu.org/licenses/>.
*/

/*** This has been tested with GTK 3.24 on Gnome ***/

/*----------------------------------------------------------
  - Perceptually uniform grey gradient on LCH color space. -
  --------------------------------------------------------*/

/* grey_00 = pure black is forbidden for visual assessment */
@define-color grey_00 #000000;
@define-color grey_05 #111111;
@define-color grey_10 #1b1b1b;
@define-color grey_15 #262626;
@define-color grey_20 #303030;
@define-color grey_25 #3b3b3b;
@define-color grey_30 #474747;
@define-color grey_35 #525252;
@define-color grey_40 #5e5e5e;
@define-color grey_45 #6a6a6a;
@define-color grey_50 #777777;
@define-color grey_55 #848484;
@define-color grey_60 #919191;
@define-color grey_65 #9e9e9e;
@define-color grey_70 #ababab;
@define-color grey_75 #b9b9b9;
@define-color grey_80 #c6c6c6;
@define-color grey_85 #d4d4d4;
@define-color grey_90 #e2e2e2;
@define-color grey_95 #f1f1f1;
@define-color grey_100 #ffffff;
/* grey_100 = pure white is forbidden for visual assessment */


/*** General rules :

  * keep just enough contrast in the UI to make text readable, but not
  too much to avoid distractions from the picture,
  * avoid sharp and unnecessary details for the same reason (shadows,
  borders, colors, fancy bits). Use plain shapes and flat design.
  * keep 50 % distance between background and foreground colors for active controls
  * keep 30 % distance between background and foreground colors for labels and info
  * keep 10 % distance between background and foreground colors for insensitive controls
  * events are ± 20 % from the normal color
  * borders and accents are ± 5 % from the background
  * buttons and control follow Google Material Design principles :
    https://material.io/design/
  * create optical margins by aligning content properly, not by using borders.
***/

/*** Naming class and ids:
  - classes should be named with underscores, starting by dt_ then what class does (main function). See below.
  Note that some remains with hyphens, they are Gtk ones and can't be changed. It's so also a good way to distinguish Gtk classes to darktable classes.
  - ids will be used with hyphens, with so item part name (for example: bauhaus-slider)
Why that? Just because it's a developer choice and most classes use underscore in code and id use hyphens. So make it easy and, more important, consistent to avoid being confused and make mistake that would cause bug just by wrong typing.
***/

/*** Using classes and ids :

  * classes are used to set same settings for multiples and different widgets in the UI.
  * ids are used to set a name to a specific widget and so allow to set CSS for this widget
  * some classes had been set on darktable, they all began with a dot. Before adding classes or ids, check what exist before

  * on those classes, some general ones had been set and should be added to your widget on Gtk code:
  - dt_transparent_background:
      - use it on a widget to set transparent background (except on hover) and no border
  - dt_no_hover:
      - don't change css on hover (for dt_module_btn)
      - ex : colorlabels on header/footer bar
  - dt_ignore_fg_state:
      - don't change foreground color on state change and don't use bg if checked
      - ex : polarity buttons in blending ui
  - dt_module_btn:
      - set for all icons (button widget without text on it)
      - ex : btn in module header
      - for toggle button this is combined with a "standard" "toggle" class
  - dt_big_btn_canvas:
      - use it for some buttons that need to have bigger margin on button-canvas
  - dt_dimmed:
      - set all icons that need to be dimmed by default (typed text in search box when checking results and color labels icons)
  - dt_section_expander:
      - use it for all expanders you could add on a module
  - dt_section_label:
      - use it for section label text (titles in modules or some popover like overlays or guides lines ones). This set mainly whiter text and bottom-border line
  - dt_monospace:
      - set monospace font for some specific items (on some tooltips, history number or colorpicker values...)
  - dt_warning:
      - set all warnings messages inside panels, like deprecated modules messages or warning we could see when having white balance applied twice with color calibration and white balance modules
  - dt_bauhaus:
      - use it to set same settings on all bauhaus (sliders and combobox)
  - dt_bauhaus_alignment:
      - used for icons that needs to be aligned with bauhaus widgets in libs
      - ex : mask display line on filmicrgb and tone equalizer
  - dt_bauhaus_popup:
      - used for all bauhaus popup
  - dt_spacing_sw:
      - use it if spacing needed around some treeview (actually used in collections and tagging modules to have spacing on top and bottom margin regarding buttons/entries around

  * the list above is not exhausting. Those are main ones set actually and should be completed if needed. You could see other classes on this file
***/

/*--------------------------------------
  - Define default colors and settings -
  --------------------------------------*/

/* General */
@define-color selected_bg_color @grey_35;       /* legacy stuff */
@define-color border_color @grey_10;            /* border, when used */
@define-color bg_color @grey_15;                /* general background */
@define-color fg_color @grey_75;                /* general text */
@define-color base_color @fg_color;             /* legacy stuff */
@define-color text_color @grey_05;              /* same */
@define-color placeholder_text_color @grey_50;  /* placeholder text color (text on search background fields) */
@define-color disabled_fg_color @grey_45;       /* disabled controls */

/* Scroll bars (sliders) */
@define-color scroll_bar_inactive @grey_40;
@define-color scroll_bar_active @grey_60;
@define-color scroll_bar_bg @grey_10;

/* Modules box (plugins) */
@define-color plugin_bg_color @grey_20;
@define-color section_label shade(@fg_color, 0.92);
@define-color plugin_label_color @grey_60;
@define-color collapsible_bg_color @grey_25;

/* Modules controls (sliders and comboboxes) */
@define-color bauhaus_bg alpha(@bg_color, 0.55);
@define-color bauhaus_fg @fg_color;       /* needed to correctly display color pickers in all states, even if they inherit @fg_color by default */
@define-color bauhaus_border shade(@plugin_bg_color, 0.5);
@define-color bauhaus_indicator_border @grey_20;
@define-color bauhaus_fill @grey_40;
@define-color bauhaus_bg_hover @grey_60;
@define-color bauhaus_fg_hover @grey_90;
@define-color bauhaus_fg_selected @grey_80;
@define-color bauhaus_fg_insensitive alpha(@fg_color, 0.4);

/* GTK Buttons and tabs */
@define-color button_bg @grey_25;
@define-color button_fg alpha(@fg_color, 0.55);
@define-color button_border shade(@button_bg, 1.04);
@define-color button_hover_bg @grey_50;
@define-color button_hover_fg @grey_15;

/* text fields */
@define-color field_bg @grey_15;
@define-color field_fg @fg_color;
@define-color field_active_bg @grey_25;
@define-color field_active_fg @grey_80;
@define-color field_selected_bg @grey_40;
@define-color field_selected_fg @grey_85;
@define-color field_hover_bg @grey_55;
@define-color field_hover_fg @grey_95;

/* Tooltips and contextual helpers */
@define-color tooltip_bg_color @grey_15;
@define-color tooltip_fg_color @grey_65;
@define-color really_dark_bg_color @grey_05;
@define-color log_fg_color @grey_85;

/* Views */
@define-color darkroom_bg_color @grey_50;    /* this need to be middle grey to correctly work on images. And for all themes */
@define-color darkroom_preview_bg_color @darkroom_bg_color;
@define-color lighttable_bg_color @grey_40;
@define-color lighttable_preview_bg_color @darkroom_bg_color;
@define-color lighttable_bg_font_color @grey_75;
@define-color print_bg_color @lighttable_bg_color;

/* Lighttable and film-strip */
@define-color thumbnail_font_color @grey_30;
@define-color thumbnail_bg_color @grey_50;
@define-color thumbnail_star_bg_color @grey_35;
@define-color thumbnail_star_hover_color @grey_80;
@define-color thumbnail_localcopy @grey_95;
@define-color thumbnail_fg_color @grey_55;
@define-color thumbnail_bg50_color @grey_95;
@define-color thumbnail_selected_bg_color @grey_70;
@define-color thumbnail_hover_bg_color @grey_85;
@define-color thumbnail_hover_fg_color @grey_90;
@define-color filmstrip_bg_color @lighttable_bg_color;
@define-color timeline_bg_color shade(@lighttable_bg_color, 0.9);
@define-color timeline_fg_color @text_color;
@define-color timeline_text_bg_color alpha(black, .8);
@define-color timeline_text_fg_color alpha(white, .9);

@define-color range_graph_color alpha(@timeline_fg_color, 0.75);
@define-color range_bg_color alpha(@fg_color, 0.12);
@define-color range_cursor_color alpha(@fg_color, 0.75);
@define-color range_sel_color alpha(@bauhaus_fill, 0.6);
@define-color range_icon_color alpha(@thumbnail_hover_fg_color, 0.35);

/* Brushes */
@define-color brush_cursor alpha(white, .9);
@define-color brush_trace alpha(black, .6);

/* Graphs : histogram, navigation thumbnail and some items on tone curve */
@define-color graph_bg @grey_15;
@define-color graph_exterior shade(@graph_bg, 0.9);
@define-color graph_border @grey_05;
@define-color graph_fg @grey_75;
@define-color graph_fg_active @grey_95;
@define-color graph_grid @grey_05;
@define-color graph_overlay @grey_50;
@define-color inset_histogram alpha(@grey_65, 0.50);
/* Primaries selected for visual legibility across a variety of
   gamuts, and to combine to pleasing secondaries. The combination of
   all three must either to either the same number or >= 255 to
   produce a neutral overlap. */
@define-color graph_red rgb(237,30,20);
@define-color graph_green rgb(28,235,26);
@define-color graph_blue rgb(14,14,233);

@define-color colorlabel_red rgb(230,0,0);
@define-color colorlabel_green rgb(0,230,0);
@define-color colorlabel_blue rgb(40,140,255);
@define-color colorlabel_yellow rgb(255,180,0);
@define-color colorlabel_purple rgb(230,0,230);

/* Reset GTK defaults - Otherwise dt inherits Adwaita default theme dark */
*
{
  background-color: @bg_color;
  background-image: none;
  color: @fg_color;
  font-size: 1em;   /* avoid changing this settings or you will made the UI quite awful (too big or too small) */
  font-family: sans-serif, "Arial Unicode MS";
  font-feature-settings: "tnum";
  font-weight: normal;
  box-shadow:none;
  outline-style:none;
  text-shadow:none;
  border: 0;
  margin: 0;
  padding: 0;
  min-width: 0;
  min-height: 0;
}

/* Be sure now than all others UI widgets have no background color on start */
combobox,
combobox *,
togglebutton,
togglebutton *,
notebook,
notebook *,
notebook tab,
notebook tab *,
table,
table *,
row,
row *,
frame,
frame *,
filechooser,
filechooser *,
filechooserdialog,
filechooserdialog *,
alignment,
entry,
entry *,
textview,
textview *,
dialog,
dialog *,
colorswatch,
colorswatch *,
stack,
stack *,
scrollbar,
scrollbar *,
scale,
scale *,
button,
button *,
treeview,
treeview *,
menu,
menu *,
separator,
eventbox,
eventbox *,
box,
box *
{
  background-color: transparent;
  font-family: sans-serif, "Arial Unicode MS";
}

/*----------------------
  - General properties -
  ----------------------*/
*:disabled
{
  color: @disabled_fg_color;
}

/* Set no background and no border. And avoid undershoot and overshoot having too much empty space at top/bottom of sidepanels when scrolled */
.combo,
.combo:disabled,
.dt_accels_box treeview,
.dt_ignore_fg_state:checked,
.dt_module_btn,
.dt_module_btn:disabled,
.dt_transparent_background,
.dt_transparent_background:checked,
.dt_transparent_background check:selected,
filechooser .sidebar-button,
menuitem:hover arrow,
popover #bauhaus-combobox,
overshoot,
undershoot,
#usercss-box textview,
#view-dropdown .combo
{
  background-color: transparent;
  border: none;
}

/* Sets outer borders that could be hide or shown in darktable with 'b' shortcut */
#outer-border
{
  background-color: @border_color;
  min-width: 1.4em;
}

/* Allow to resize all windows */
decoration
{
  margin: 1px; /* need to remains as px as just minimum size needed, no need to be scalable */
}

/* Default Gtk buttons and other entries */
button,
checkbutton check,
entry,
textview,
#non-flat
{
  padding: 1px; /* this need to be kept as pixels for constant sizing and correct display with all font sizes */
  border: 1px solid @button_border; /* this too */
  border-radius: 0.21em;
  background-color: @button_bg;
  font-family: sans-serif, "Arial Unicode MS";
}

/* then set specific settings for all text zones and combo */
.combo,
checkbutton check,
entry,
spinbutton button,
textview
{
  background-color: alpha(@fg_color, 0.09);
  margin: 0.07em;
}

.combo,
entry,
#dt-metadata-multi,
textview
{
  padding: 0.07em 0.28em;   /* be sure to keep 0.07em padding, at least to scrolledwindow textview part. part of workaround just below */
}

.combo *
{
  min-height: 1em;
  min-width: 1em;
}

/* workaround for specific Gtk issue since 3.24.24 on scrolledwindow textview as borders are not displayed */
scrolledwindow textview
{
  border: none;
}

/* Buttons in modules header, path-bar on filechooser dialog window and all text-button and related */
.dt_module_btn,
.path-bar button,
header button,
.image-button,
.text-button,
.text-button radio, /* this line and following one are needed to set good margin inside some buttons, especially in header buttons in about dialog window and on radio button and label inside star icon overlay menu */
.text-button label
{
  min-height: 1.15em;
  min-width: 1.15em;
  margin: 0.07em;
}

.dt_module_btn,
.dt_ignore_fg_state:checked
{
  color: @button_fg;
}

spinbutton > button,
#top-hinter box
{
  margin: 0.07em;
}

/* set disabled options of above settings */
button:disabled,
spinbutton *:disabled,
#collapsible spinbutton button:disabled,
#non-flat:disabled
{
  background-color: transparent;
  border: 0.07em solid alpha(@button_border, 0.35);
  color: @disabled_fg_color;
}

popover spinbutton  /* needed to set some margin on overlay popover between it and label */
{
  margin-left: 0.35em;
}

/* Default extra margin for icons optical alignment */
#button-canvas
{
  margin: 5px; /* not real pixels. This is used as a percent extra space and couldn't be scalable */
}

/* fix misalignment for specific icons related to bauhaus widgets */
.dt_plugin_ui .dt_bauhaus_alignment,
.dt_plugin_ui .dt_bauhaus_alignment #button-canvas
{
  margin: 0;
}

/* Default text fields and text boxes */
entry,
textview
{
  font-family: sans;
}

label selection,
textview text selection
{
  background-color: @field_selected_bg;
}

entry selection
{
  background-color: shade(@field_selected_bg, 1.1);
}

/* Separator */
separator
{
  border-left: 0.07em solid transparent;
}

/* set lighter opacity for specific items that need it (ex: color labels icons) */
.dt_dimmed
{
  opacity: 0.6;
}

.dt_dimmed:hover, /* to set full opacity when hovering those items */
.dt_dimmed:checked
{
  opacity: 1;
}

/*------------------------
  - Main header settings -
  ------------------------*/
/* Labels in views */
#view-label,
#view-dropdown
{
  font-size: 1.5em;
  border-radius: 0.14em;
  color: @disabled_fg_color;
}

/*------------------------------
  - Header and footer toolbars -
  ------------------------------*/
#header-toolbar,
#footer-toolbar
{
  padding: 0.14em 0.28em;
}

/* Set toolbars buttons in lighttable and darkroom views ; and top right button in accels window */
.dt_accels_stick,
#filter-colors-box .dt_module_btn,
#header-toolbar .dt_module_btn,
#footer-toolbar .dt_module_btn
{
  min-height: 1.7em;
  min-width: 1.7em;
}

/* Rating stars on left footer toolbar on lighttable view */
#lib-rating-stars
{
  min-height: 1.1em;
  min-width: 9em;
  margin-right: 1em;
}

/* Set default button spacing  for specific buttons */
.dt_section_expander #button-canvas,
.dt_big_btn_canvas #button-canvas,
#module-header #button-canvas
{
  margin: 12px; /* not real pixels. This is used as a percent extra space and couldn't be scalable */
}

/*---------------------------
  - Side panels and modules -
  ---------------------------*/
/* Frame around modules boxes */
/*** NOTE: bauhaus controls inherit their font properties from there ***/
.dt_plugin_ui,
dialog,  /* dialog window background */
dialog .sidebar row:selected,   /* .sidebar lines here allow to select same settings for sidebar in preferences and filechooser */
dialog .sidebar row:selected:hover label,
#bauhaus-slider,  /* be sure there's some visible "spacing" around arrow on sliders by setting specific background on bauhaus-slider */
#background-job-eventbox  /* background of export progress bar */
{
  background-color: @plugin_bg_color;
}

.dt_plugin_ui_main,
#blending-box,
#guides-module-combobox
{
  padding: 0.28em 0.65em;
}

/* Frame around blending boxes */
#blending-tabs + #blending-box /* set top margin below blending tabs */
{
  margin-top: 0.14em;
}

/* Add some space to aerate if a notebook follows a widget (like for example in filmic rgb module */
.dt_plugin_ui_main widget + notebook
{
  margin-top: 0.5em;
}

/* Gradient sliders */
.dt_gslider,
.dt_gslider_multivalue
{
  min-height: 1.7em;
  padding: 0.56em;
}

/* Set duplicate module space in darkroom */
.dt_duplicate_ui grid,
.dt_duplicate_ui grid > label
{
  margin: 0.28em 0.14em;
}

.dt_duplicate_ui entry
{
  margin: 1em 0 1em 0.5em;
}

/* Set modules name header */
#module-header
{
  border-bottom: 1px solid @plugin_bg_color;
  padding: 0.5em;
}

/* Labels in modules */
#iop-panel-label,
#lib-panel-label
{
  color: @plugin_label_color;
  padding: 0 0.14em 0.14em 0.45em;
  font-weight: normal;
  font-family: sans-serif, "Arial Unicode MS";
  font-size: 1.1em;
}

#snapshot-button entry,
#iop-module-name
{
  font-weight: lighter;
  font-family: sans-serif, "Arial Unicode MS";
  font-size: 0.8em;
}

#iop-module-name-error
{
  font-weight: bold;
  font-size: 1.1em;
  padding-left: 0.5em;
  color: red;
}

.dt_module_focus #iop-panel-label/* set focus mode */
{
  color: shade(@plugin_label_color, 1.2);
}

/* Labels of controls sections in modules */
.dt_section_label:not(#blending-box),
#blending-box .dt_section_label label, /* we need to separate what we apply on label and not after for those lines as they have icons on it */
#blending-box .dt_section_label .dt_bauhaus  /* same here for drawn mask line */
{
  padding: 0.14em 0;
  color: @section_label;
  font-family: sans-serif, "Arial Unicode MS";
  font-weight: 500;
}

.dt_section_label
{
  border-bottom: 0.05em solid @section_label;
  margin: 0.21em 0;
}

/* Collapsible sections on lib & iop modules */
#collapse-block
{
  margin: 0.21em 0;
}

#collapsible
{
  border: 0.07em solid shade(@collapsible_bg_color, 1.05);
  border-top: none;
  padding: 0.28em;
}

#collapsible,
#collapsible #bauhaus-slider
{
  background-color: shade(@collapsible_bg_color, 1.04);
}

.dt_section_expander {
  padding: 0.07em 0 0.07em 1.7em;  
}

.dt_section_expander,
.dt_section_expander label
{
  background-color: @collapsible_bg_color;
  border: none;
  margin-top: 0.14em;
}

.dt_section_expander label {
  padding: 0.07em 0;
}

/*-------------------
  - Scope/Histogram -
  -------------------*/
#main-histogram .button_box
{
  margin: 0.21em;
}

#main-histogram .dt_module_btn
{
  min-height: 1em;
  min-width: 1em;
}

/* set background color and color of scope/histogram buttons */
#main-histogram .dt_module_btn:not(.rgb_toggle)
{
  color: alpha(@field_selected_fg, 0.56);
  background-color: alpha(darker(@button_bg),0.8);
  border: 0.07em solid alpha(@button_bg, 0.33);
}

/* set channel buttons at inactive state */
#red-channel-button
{
  background-color: alpha(@graph_red, 0.2);
}

#green-channel-button
{
  background-color: alpha(@graph_green, 0.2);
}

#blue-channel-button
{
  background-color: alpha(@graph_blue, 0.2);
}

/* set now them active state */
#red-channel-button:checked
{
  background-color: alpha(@graph_red, 0.66);
}

#green-channel-button:checked
{
  background-color: alpha(@graph_green, 0.66);
}

#blue-channel-button:checked
{
  background-color: alpha(@graph_blue, 0.66);
}

/* set now hover state */
#red-channel-button:hover
{
  background-color: alpha(@graph_red, 0.5);
}

#green-channel-button:hover
{
  background-color: alpha(@graph_green, 0.5);
}

#blue-channel-button:hover
{
  background-color: alpha(@graph_blue, 0.5);
}

/*------------------
  - Dialog windows -
  ------------------*/
/* Set default settings */
.dt_accels_window,
dialog .dialog-vbox
{
  margin: 0.42em;
}

/* set top margin in bottom button bar to be the same as bottom margin applied above for main dialog window */
dialog .dialog-action-box,
#shortcut-controls,
#preset-controls
{
  margin-top: 0.42em;
}

dialog .dialog-vbox .text-button:not(.toggle)
{
  margin: 0.21em;
}
/* remove margin on the left for first left button only, then do the same for right margin for last right button only */
dialog .dialog-action-box button:first-child
{
  margin-left: 0;
}

dialog .dialog-vbox button:last-child:not(.toggle)
{
  margin-right: 0;
}

/* remove left and reduce top margin only on dialog filechooser box */
dialog filechooser
{
  margin-top: -0.21em;
  margin-left: -0.42em;
}

/* Set title headerbar for some themes and/or desktops environment */
dialog headerbar
{
  background-color: @border_color;
  padding: 0.2em 0.6em;
}

dialog headerbar entry
{
  margin: 0.56em;
}

/*------------------------------------------------
  - Context menu, bauhaus, tooltips & comboboxes -
  ----------------------------------------------*/
/*** so nearly everything that pop out */
.popup,
context-menu,
menu,
menuitem > arrow,
tooltip,
popover,
popover #bauhaus-slider,
#range-current
{
  opacity: 1;  /* this is needed for tooltip on/off feature with Shift+t shortcut */
  background-color: @tooltip_bg_color;
  border-radius: 0.14em;
  min-height: 0.4em;
  min-width: 0.4em;
}

menu
{
  padding: 0.14em 0;
}

.popup,
popover
{
  padding: 0.42em;
}

/* Adjust check menuitems */
menuitem check,
menuitem check:selected
{
  min-width: 0.7em;
  min-height: 0.7em;
  margin: 0 0.35em;
}

/* set default settings on bauhaus popup, items that can be selected : items inside categories titles set above */
.dt_bauhaus_popup
{
  color: shade(@fg_color, 0.9);
  background-color: @tooltip_bg_color;
  padding: 0.42em 0 0 0.42em;   /* set only padding on top and left margin due to some default hardcoded spacing on bottom and place for icons on some bauhaus on the right */
}

.dt_bauhaus_popup_right /* set right padding for specific popup with no hardcoded right space due to icons place */
{
  padding-right: 0.42em;
}

/* fix for menuitem in bottom toolbar mainly */
menuitem > arrow
{
  padding-right: 0.42em;
}

/* then spacing around bauhaus widgets */
.dt_bauhaus
{
  margin: 0.14em 0;
}

/* and set menuitem related to sort of combobox like metadata presets in import module */
menuitem label,
#view-dropdown button,
#view-dropdown menuitem,
#view-label
{
  padding: 0.07em 0.42em;
  margin: 0;
}

tooltip,
#range-current
{
  border: 0.07em solid shade(@tooltip_bg_color, 1.5);
  border-radius: 0;
}

tooltip label,
#range-current label
{
  color: @tooltip_fg_color;
}

tooltip separator
{
  margin: 0.6em 2em;
  padding-bottom: 0.07em;
  background-color: @selected_bg_color;
}

combobox label
{
  margin: 0 0.35em 0 0;
}

menu separator,
popover separator
{
  margin: 0.6em 2em;
  padding-bottom: 0.07em;
  background-color: @selected_bg_color;
}

combobox separator
{
  min-height: 0.07em;
}

/*----------------------
  - GTK Notebooks tabs -
  ----------------------*/
notebook tabs,
#blending-tabs,
#guides-line,
#modules-tabs
{
  font-family: sans-serif, "Arial Unicode MS";
}

#blending-tabs,
#guides-line,
#modules-tabs
{
  background-color: @field_active_bg;
}

/* set sizes of "buttons" in modules and blending tabs ; and guides lines too */
#modules-tabs .toggle
{
  min-height: 2em;
}

.dt_plugin_ui #blending-tabs .dt_module_btn,
#guides-line .toggle
{
  min-height: 1.3em;
}

.dt_plugin_ui #blending-tabs .dt_module_btn,
#guides-line .dt_module_btn,
#modules-tabs .dt_module_btn   /* This is needed separately to set a minimal width if panel is really narrow */
{
  border-radius: 0;
  min-width: 1.5em;
  margin: 0;
}

/* reduce and align now hamburger menu of modules and blending tabs */
.dt_plugin_ui #blending-tabs .dt_module_btn,
#modules-tabs .dt_module_btn
{
  padding: 0.3em;
}

/* Set notebook tabs */
notebook tab label
{
  border-bottom: 0.1em solid transparent;
  color: shade(@field_selected_fg, 0.75);
  padding: 0.28em 0.07em;
}

notebook tab:hover label
{
  border-bottom-color: shade(@field_selected_fg, 0.7);
  color: shade(@field_selected_fg, 0.9);
}

notebook tab:checked label
{
  border-bottom-color: @field_selected_fg;
  color: @field_selected_fg;
}

notebook header /* add space between notebook tabs and options below */
{
  margin-bottom: 0.42em;
}

/*--------------------------
  - GTK sliders and scales -
  --------------------------*/
/*** WARNING : sliders IN modules are bauhaus (from a custom lib in darktable), not standard GTK sliders (see below) ***/
scrollbar slider
{
  background-color: @scroll_bar_inactive;
  border: 0.1em solid transparent;
}

scrollbar.horizontal slider
{
  min-width: 1em;
  min-height: 0.45em;
}

scrollbar.vertical slider
{
  min-width: 0.45em;
  min-height: 1em;
}

scale contents trough
{
  background-color: @scroll_bar_bg;
  border-radius: 0.21em;
  min-width: 0.5em;
  min-height: 0.5em;
}

scale contents trough highlight
{
  background-color: @button_bg;
  min-height: 0;
  min-width: 0;
}

scale contents trough slider
{
  background-color: @scroll_bar_inactive;
  border: 0.1em solid @border_color;
  font-size: 1em;
  min-height: 0.5em;
  min-width: 0.5em;
}

scrollbar
{
  border-color: @scroll_bar_bg;
  background: @scroll_bar_bg;
  border-radius: 0.42em;
  min-height: 0.5em;
  min-width: 0.5em;
  margin: 0.07em;
}

/* Scrollbars hover state */
scrollbar slider:hover,
scale contents trough slider:hover
{
  background-color: @scroll_bar_active;
}

/*----------------------------------------------------------
  - Tree view (lists and tables) and accel window reminder -
  --------------------------------------------------------*/
/* Set treeview header on all windows, including preferences and accels window */
treeview,
filechooser widget treeview
{
  background-color: @field_bg;
}

treeview header label
{
  font-weight: bold;
  padding: 0.14em;
}

/* set margin around treeview (need to be applied on scrolledwindow which is a direct parent) */
.dt_spacing_sw scrolledwindow
{
  margin: 0.21em 0;
}

/* Set main title in top of accel window reminder */
.dt_accels_cat_title
{
   background-color: transparent;
   font-weight: bold;
   font-size: 1.1em;
   padding: 0.7em 0;
}

/*-------------------------------------
  - Preferences dialog window options -
  -------------------------------------*/
#preferences-content,
#preferences-content .dialog-action-box,
#quick-presets-manager box  /* set same setting on quick preset manager dialog window ; accessible to bottom left button on footer bar in darkroom view */
{
  margin: 0; /* reset default dialog margin to main items. And needed to have an hover and selected effect on all width of categories in sidebar */
}

/* Set preferences margins in main part */
#preferences-box stack > box,
#preferences-box stack > scrolledwindow
{
  padding: 0.7em 1.2em;
}

/* Edit CSS view on preferences dialog window */
#usercss-box
{
  border: 0.07em inset @border_color;
  background-color: shade(@bg_color, 1.1);
  margin-top: 0.7em;
  padding: 0.7em;
}

/* Set text size */
#preferences-notebook scrolledwindow label /* label text on main tabs */
{
  min-height: 1.5em;
}

#preferences-notebook scrolledwindow #pref_section > label  /* sub-category title in main content */
{
  font-size: 1.2em;
  font-weight: bold;
}

/* Just add spacing between label settings and setting beside */
#preferences-notebook stack widget label
{
  margin-right: 1.75em;
}

#preference_non_default
{
  font-weight: bolder;
  margin: 0.07em;
  min-width: 1em;
}

#preset-controls entry,
#shortcut-controls entry
{
   min-width: 15em;
}

/* Set buttons and search field on presets and shortcuts control buttons */
#shortcut-controls .search,
#preferences-box #shortcut-controls check
{
  margin-right: 0.28em;
}

/*--------------------------------
  - Module layouts manager dialog -
  --------------------------------*/
/* set top box header */
#modulegroups-top-boxes
{
  margin-right: 6em;
}

/* set main group modules title */
#modulegroups-groups-title label
{
  font-size: 1.1em;
}

#modulegroups-groups-title label,
#modulegroups-header-center entry
{
  font-weight: bold;
  margin-right: 0.28em;
}

#modulegroups-groups-title,
#modulegroups-groups-box
{
  margin-top: 0.42em;
}

#modulegroups-groups-box
{
  border-top: 0.035em solid  @section_label;
  padding-top: 0.5em;
}

/* set groups part, starting by setting same margin around top part and group boxes */
#modulegroups-topbox,
#modulegroups-groupbox
{
  margin: 0 0.5em;
}

#modulegroups-header-center,
#modulegroups-iop-header
{
  background-color: shade(@bg_color, 1.05);
  padding: 0.28em;
}

/* now set borders around group header and group list */
#modulegroups-header-center
{
  border: 0.07em solid @bg_color;
  border-radius: 0.5em 0.5em 0 0;
  margin-bottom: 0.14em;
}

#modulegroups-iop-header:first-child
{
  border-top: 0.07em solid @bg_color;
  border-radius: 0;
  margin-top: 0.14em;
}

#modulegroups-iop-header
{
  border-left: 0.07em solid @bg_color;
  border-right: 0.07em solid @bg_color;
}

#modulegroups-iop-header:last-child
{
  border-bottom: 0.07em solid @bg_color;
  border-radius: 0 0 0.5em 0.5em;
}

/* set group icons */
#modulegroups-group-icon,
#modulegroups-icons-popup .dt_module_btn
{
  min-height: 1.8em;
  min-width: 1.8em;
  margin: 0 0.7em 0 0.07em;
}

/*---------------------------------------------------------------
  - Set sidebars settings on preferences window and filechooser -
  ---------------------------------------------------------------*/
/* Set default sidebars settings */
filechooser .sidebar,
#preferences-box .sidebar scrolledwindow
{
  padding: 0; /* needed to have an hover and selected effect on all width of categories in sidebar */
  font-size: 1.1em;
  background-color: @bg_color;
}

#preferences-box .sidebar row
{
  padding: 0.35em;
}

/* Set lines states */
filechooser .sidebar-icon,
filechooser .sidebar-label
{
  padding: 0.42em;
}

filechooser .sidebar-button /* set icons displayed in the right of the sidebar, like eject buttons */
{
  margin-right: 0.28em;
}

filechooser row
{
  margin-top: -0.28em; /* be sure to not have empty space on top of row for hover and selected effects */
}

#preferences-box .sidebar row,
#preferences-box .sidebar row:selected
{
  border-left: 0.14em solid @bg_color; /* be sure border is set but not visible if category on sidebar not selected but keep same size and type for selected category ; color needs to be same as sidebar scrolledwindow background-color few lines above */
}

filechooser row:selected .sidebar-icon,  /* set icon instead of border for filechooser dialog window */
filechooser row:hover .sidebar-icon
{
  color: @button_hover_fg;
  background-color: @field_hover_bg;
}

#preferences-box .sidebar row:selected
{
  border-left-color: @field_hover_bg;   /* make the border left visible with chosen color if category on sidebar is selected */
}

#preferences-box .sidebar label
{
  padding: 0.14em 0.84em;
}

/*--------------------
  - Quick access tab -
  --------------------*/
#basics-box-labels widget
{
  border-top: 0.07em solid @lighttable_bg_color;
}

#basics-header-box,
#basics-module-hbox > box
{
  padding: 0.21em 0.7em;
}

#basics-box-labels widget:first-child,
#basics-box-labels .dt_section_label,
#thumb-localcopy:active
{
  border: none;
}

#basics-widget #toneeqgraph
{
  min-height: 18em;
}

/*--------------------------------------------------
  - Collect module and quick filters on header bar -
--------------------------------------------------*/
/* set main settings and module part */
#range-select #dt-range-band
{
  border: 0.07em solid alpha(@range_bg_color, 0.9);
  border-radius: 0.21em;
  min-height: 1.6em;    /* band height */
  min-width: 9em;
  margin: 0.1em 0;      /* be sure it's set same margin as bauhaus-combobox to have same height in quick filters box with sortby combobox */
  padding: 0.07em;
}

#module-filtering #collect-rule-widget   /* set filter widget box */
{
  background-color: @collapsible_bg_color;
  margin: 0.2em 0;
  padding: 0.07em 0.28em;
  border-radius: 0.21em;
}

#module-filtering #filter-sort-box
{
  padding: 0.28em 0;
}

/* set spacing around date bottom popup when right-clicking dates widgets */
#dt-range-date-popup box:last-child
{
  margin-top: 0.35em;
}

#dt-range-date-popup treeview,
#dt-range-date-popup box:last-child button
{
  margin-left: 0.35em;
}

#dt-range-date-popup .text-button,
#dt-range-date-popup entry,
#dt-range-date-popup treeview
{
  background-color: alpha(@fg_color, 0.14);
  border-color: alpha(@fg_color, 0.09);
}

/* set range parts and quick filter box in header toolbar */
.search,
#dt-range-band,
#header-toolbar #bauhaus-combobox:not(.dt_transparent_background)
{
  background-color: @range_bg_color;
}

/* and adjust label and color buttons, especially in header bar */
.dt_quick_filter,
#filter-sort-box label,
#header-rule-box label,
#header-sort-box label,
#header-toolbar .dt_module_btn
{
  margin-right: 0.56em;
}

#header-toolbar .dt_module_btn
{
    margin-left: 0;
    padding-left: 0;
}

#filter-colors-box .dt_module_btn
{
  padding: 2px 0;
  margin: 0;
}

#range-current
{
  padding: 0.2em;
}

#collect-header-box button
{
  margin-right: 0;
}

#dt-range-band-graph
{
  color: @range_graph_color;
}

#dt-range-band-selection
{
  color: @range_sel_color;
}

#dt-range-band-icons
{
  color: @range_icon_color;
}

#dt-range-band-cursor
{
  color: @range_cursor_color;
}

#dt-range-rating #dt-range-band-cursor,
#dt-range-rating #dt-range-band-selection
{
  color: transparent;
}

#dt-range-band-icons:active
{
  color: @bauhaus_fg;
}

#dt-range-band-icons:hover
{
  color: @bauhaus_fg_hover;
}

/*------------
  - Map view -
  ------------*/
/* Set how to display result list on find location module */
#dt-map-location > *
{
margin: 0.14em;
}

#dt-map-location label
{
padding: 0.14em;
}

#dt-map-location
{
border-bottom: 0.07em solid alpha(@selected_bg_color, 0.75);
}

#dt-map-location:hover
{
  background-color: @field_hover_bg;
}

#dt-map-location:hover label
{
  color: @field_hover_fg;
}

/*---------------
  - Other stuff -
  ---------------*/
/*** Some tags below inherit from properties above ; so avoid moving that part ***/
/* Set specific content */
#import-presets,
#modulegroups-ro
{
  margin-top: 1.0em;
  font-weight: bold;
}

/* About window: set different background for credits view */
#about-dialog scrolledwindow
{
  background: @bg_color;
}

#about-dialog grid *
{
  padding: 0.14em;
}

/* Special modules: image infos, navigation and search ones */
#image-info /* in darkroom footerbar */
{
  font-size: 1.1em;
}

#navigation-module
{
  background-color: @graph_bg;
}

#nav-zoom
{
  background-color: alpha(@darkroom_bg_color, 0.25);
  color: @graph_fg_active;
  border-top-left-radius: 0.21em;
  margin: 0;
}

#nav-zoom:hover
{
  background-color: alpha(@darkroom_bg_color, 0.5);
}

/* search boxes (background color is set on collect module part above ; to be the same as other filters in quick filter bar) */
#right .search  /* set only search box margin in modules panel */
{
  margin: 0.14em 0.28em;
}

.search label
{
  margin-right: 0.5em;
}

.search image.left
{
  padding: 0 0.4em;
}

.search image:disabled
{
  color: shade(@fg_color, 0.75);
}

/* set "buttons" combo in lens correction module */
#lens-module .text-button
{
  background-color: alpha(@fg_color, 0.06);
  min-height: 1em;
}

/* Progress bar on bottom left side when exporting, importing, etc... */
#background-job-eventbox label
{
  margin: 0.07em 0.28em;
}

progressbar trough
{
  background-color: @scroll_bar_inactive;
}

progressbar progress
{
  background-color: @scroll_bar_active;
  border-radius: 0.28em;
}

/* Be sure to have minimum width allowed for infos on images infos module if reducing panel width */
#brightbg
{
  min-width: 5.6em;
  padding-left: 0.14em;
}

/* Set color bar min height in colorzones module */
#iop-bottom-bar
{
    min-height: 1.2em;
}

/* Set messages: first those on bottom middle of the UI, for example "loading image..." or "working on..." ; then deprecated and warning messages */
.dt_messages
{
  color: @log_fg_color;
  font-weight: bold;
  background-color: alpha(@plugin_bg_color,0.5);
  padding: 0.35em 0.7em;
  border-radius: 0.35em;
}

.dt_warning
{
  background-color: #852A2A;
  padding: 0.42em;
}

/* then set infos shown on top of the image on darkroom, like for example opacity in drawn masks */
.top .dt_messages
{
  border-radius: 0 0 0.56em 0.56em;
}

/* Set some specific fonts */
.dt_monospace
{
  font-family: monospace;
}

/* Set below history items in darkroom */
/* the label buttons */
.dt_history_items
{
  padding: 0 0.21em;
  margin: 0.14em;
}

/* then the switches */
.dt_history_switch:disabled /* state of those switch is always disabled */
{
  color: @plugin_label_color;
  font-size: 0.75em;
  margin-left: 0.42em;
  border: 0;
}

/* and the history number */
#history-number
{
  padding: 0 0.35em 0 0;
}

/* deactivated switch buttons, make them less visible */
.dt_history_switch_off,
.dt_history_switch_off:disabled
{
  color: @disabled_fg_color;
}

#dt-metadata-changed
{
  font-style: italic;
  text-decoration-line: underline;
}

#dt-metadata-multi
{
  font-style: italic;
  margin: 0.07em; /* match textview */
}

/*---------------------
  - Set color pickers -
  ---------------------*/
/** Module in darkroom left panel **/
/* the main color picker button */
#color-picker-button
{
  min-height: 1.5em;
  min-width: 1.5em;
  border: 0;
}

#color-picker-area
{
  min-height: 5em;
  margin-bottom: 0.14em;
}

#live-sample
{
  margin: 0.21em 0.21em 0.21em 0;
  min-width: 2em;
}

/* Color picker visibility for levels and rgb levels modules.
   be careful to not change that unless you really now what you do */
#picker-black
{
  color: @grey_05;
  border: 0;
}

#picker-grey
{
  color: @grey_50;
  border: 0;
}

#picker-white
{
  color: @grey_95;
  border: 0;
}

/*------------------------------------------------------------------
  - Pseudo-classes : need to be defined last to not be overwritten -
  ------------------------------------------------------------------*/
/*** All states options are designed below, active, hover, selected, disabled and check states ***/
/* Main states */
.default, /* dialog button that activates with Enter */
.dt_history_items:active,
.dt_history_items:checked,
button:active,
button:checked:not(:disabled):not(.dt_transparent_background):not(.dt_ignore_fg_state),
button:selected,
cell:selected,
checkbutton check:checked,
combobox window *:active,
combobox window *:selected,
entry:checked,
filechooser *:selected,
menuitem:active,
menuitem:selected,
treeview *:selected:not(check),
treeview *:checked:not(check),
#main-histogram .dt_module_btn:checked:not(.rgb_toggle)
{
  background-color: @field_selected_bg;
  color: @field_selected_fg;
}

/* History currently selected button ; they don't need to set background */
.dt_history_items:active *,
.dt_history_items:checked *,
button.dt_transparent_background:checked:not(:disabled):not(.dt_ignore_fg_state)
{
  color: @field_active_fg;
}

/* Hover states, some needed background only */
.dt_history_items:hover,
.dt_transparent_background:hover,
button:hover:not(.combo):not(.dt_no_hover),
dialog .sidebar :hover,
menuitem:hover,
notebook tab:hover,
radiobutton:hover label,
treeview:not(#lutname) *:hover:not(check),
#dt-range-date-popup .text-button:hover,
#lens-module .text-button:hover,
#main-histogram .dt_module_btn:hover:not(.rgb_toggle),
#non-flat:hover,
#view-label:hover,
#view-dropdown .combo:hover
{
  background-color: alpha(@button_hover_bg, 0.5);
  border-color: transparent;
  color: shade(@fg_color, 1.15);
}

button:hover:checked:not(.combo):not(.dt_no_hover) /* allow different background on hover+active state */
{
  background-color: alpha(@button_hover_bg, 0.8);
}

.combo:hover,
.combo:hover cellview,
.dt_bauhaus:hover,
combobox window *:hover,
menuitem:hover > arrow
{
  color: shade(@fg_color, 1.2);
}

.dt_history_switch_off:hover  /* set specific hover effect for disabled modules in history stack */
{
  color: shade(@fg_color, 0.8);
}

/* workaround for a macOS quartz GTK backend bug
   making combobox list appear behind dialog window:
   color of "button:checked cellview" has to differ from
   "button:hover cellview", don't ask for a reason for that */
button:checked cellview
{
  color: shade(@button_hover_fg, 0.99);
}

/* Treeviews, entries and filechoosers states */
menuitem:not(.dt_transparent_background) check,  /* be sure transparent background is applied to preset and blend menus */
treeview check, /* set specific background color about check buttons like in copy/paste dialog window */
treeview entry, /* be sure entry is displayed without alpha background on treeview */
treeview spinbutton /* and same thing for spinbutton */
{
  background-color: @field_active_bg;
  border-color: @border_color;
}

.changed,
entry:active,
filechooser *:active,
treeview *:active
{
  background-color: @field_active_bg;
  color: @field_active_fg;
}

#preferences-box .dt_bauhaus,
treeview check:selected
{
  background-color: @button_bg;
  border-color: @plugin_bg_color;
}

menuitem check:hover,
treeview check:hover
{
  color: @bauhaus_fg_hover;
}

entry:hover
{
  color: @field_hover_fg;
  background-color: @field_hover_bg;
}

/* Adjust delete folder dialog window */
treeview#delete-dialog,
treeview#delete-dialog:selected
{
  background-color: shade(@bg_color, 0.9);
  color: @field_active_fg;
  padding-left: 1em;
}

  /* set active menu items: needed for some Pango issues not rendering synthetic bold for all OS */
.active_menu_item label
{
  font-weight: bold;
}

/* Views links states in header */
.dt_bauhaus_popup:selected,
#view-label:selected
{
  color: @fg_color;
}

/* Bauhaus states */
.dt_bauhaus_popup:disabled /* set categories titles in bauhaus combobox popup ; for example in collections module */
{
  color: white;
}

.dt_bauhaus_popup:hover
{
  color: @bauhaus_fg_hover;
}

.module_drag_icon
{
  border: 0.07em solid @plugin_bg_color;
  background-color: @bg_color;
}

.module_drop_before
{
   border-bottom: 2.4em solid @plugin_bg_color;
}

.module_drop_after
{
   border-top: 2.4em solid @plugin_bg_color;
}


/*** --- Thumbtable css part ---

Hierarchy :

  #thumbtable-filemanager / #thumbtable-filmstrip / #thumbtable-zoom (GtkLayout -- main widget which contains all thumbs)
    #thumb-main                                                      (GtkOverlay -- thumbnail widget)
      #thumb-back                                                    (GtkEventBox -- thumbnail background)
      #thumb-ext                                                     (GtkLabel -- thumbnail extension)
      #thumb-image                                                   (GtkDrawingArea -- thumbnail image)
      #thumb-cursor                                                  (GtkDrawingArea -- top triangle to show active images in filmstrip)
      #thumb-bottom                                                  (GtkEventBox -- background of the bottom infos area)
        #thumb-bottom-label                                          (GtkLabel -- text of the bottom infos area, just with .dt_extended_overlay)
      #thumb-reject                                                  (GtkDarktableThumbnailBtn -- Reject icon)
      #thumb-star                                                    (GtkDarktableThumbnailBtn -- Star icon, 5 occurrences of this)
      #thumb-colorlabels                                             (GtkDarktableThumbnailBtn -- colorlabels icon, 1 icon with all labels)
      #thumb-localcopy                                               (GtkDarktableThumbnailBtn -- local copy triangle icon)
      #thumb-altered                                                 (GtkDarktableThumbnailBtn -- altered icon)
      #thumb-group-audio                                             (GtkDarktableThumbnailBtn -- grouping and audio sidecar icons)

Details :

  #thumbtable-filemanager / #thumbtable-filmstrip / #thumbtable-zoom
    class = .dt_thumbtable          present for all thumbtable types
    class = .dt_thumbtable_reorder  if we are dragging images in custom reordering mode
    Classes to determine how are shown overlays on/under the image :
    class = .dt_overlays_none / .dt_overlays_hover / .dt_overlays_hover_extended / .dt_overlays_always / .dt_overlays_always_extended / .dt_overlays_mixed / .dt_overlays_hover_block
    Classes to determine the category of size of the thumbnails (cat sizes limits are defined in dartablerc key=plugins/lighttable/thumbnail_sizes
    class = .dt_thumbnails_?? where ?? is the cat number
  #thumb-main
    state = :selected  if the thumb is in current selection
    state = :active    if the thumb correspond to one of the images worked on (filmstrip)
    state = :hover     if the mouse hover the widget
    class = .dt_group_left / .dt_group_right / .dt_group_top / .dt_group_bottom  if we need to draw group borders around thumb
    class = .dt_thumbnail_rating_[0-6]   rating value of the image (6==rejected)
  #thumb-image
    only the borders are drawn - background is ignored
    color transparency is used to draw the image
  all icons inside the widget
    class = .dt_thumb_btn
    state = :hover : the mouse is hovering the icon
    if color and background-color are transparent, the icon is not drawn and inactive
  #thumb-reject :
    state = :active   if the image is actually rejected
    state = :hover    the mouse is hover the icon (a circle is added around the cross)
  #thumb-star :
    - color is the color of the star outline.
    - background-color is the inner part of the star
    state = :active : the rating of the image is >=
  #thumb-group :
    state = :active : the image is the group leader
***/


/*----------------------------
  - Thumbtable Main Settings -
  ----------------------------*/
/* Background */
.dt_thumbtable,
.dt_fullview
{
  background-color: @lighttable_bg_color;
}

#thumb-back
{
  background-color: @thumbnail_bg_color;
  border: 1px solid @lighttable_bg_color;
}

/* set selected image and/or focus one */
#thumb-main:active #thumb-back,
#thumb-main:selected #thumb-back
{
  background-color: shade(@thumbnail_selected_bg_color, 1.05);
  border-color: shade(@thumbnail_selected_bg_color, 1.15);
}

/* set hover effect */
#thumb-main:hover #thumb-back
{
  background-color: @thumbnail_hover_bg_color;
}

/*-----------------
  - Image options -
  -----------------*/
#thumb-image
{
  margin: 12px 10px; /* not real pixels. This is used as a per thousand of the thumb size and couldn't be scalable */
}

.dt_thumbnails_1 #thumb-image
{
  margin: 18px 16px; /* not real pixels. This is used as a per thousand of the thumb size and couldn't be scalable */
}

/* Set top margin on active image in filmstrip */
#thumbtable-filmstrip #thumb-main:active #thumb-back
{
  border-top: 0.105em solid @border_color;
}

/* Set top cursor and bottom-label */
#thumb-bottom-label,
#thumb-cursor
{
  color: transparent;
}

#thumbtable-filmstrip #thumb-main:active #thumb-cursor
{
  color: @border_color;
}

/* Set border color around grouped thumbnails */
.dt_group_left #thumb-back,
#thumb-main.dt_group_left:selected #thumb-back
{
  border-left: 0.07em solid rgb(255, 187, 0);
}

.dt_group_top #thumb-back,
#thumb-main.dt_group_top:selected #thumb-back
{
  border-top: 0.07em solid rgb(255, 187, 0);
}

.dt_group_right #thumb-back,
#thumb-main.dt_group_right:selected #thumb-back
{
  border-right: 0.07em solid rgb(255, 187, 0);
}

.dt_group_bottom #thumb-back,
#thumb-main.dt_group_bottom:selected #thumb-back
{
  border-bottom: 0.07em solid rgb(255, 187, 0);
}

.dt_thumbtable_reorder #thumb-main:hover #thumb-back
{
  border-left : 4px solid rgb(255, 187, 0);
}

/*----------------------------------------
  - Popover overlays menu from star icon and grid lines -
  ----------------------------------------*/
#show-tooltip
{
  margin: 0.28em 0 0 0.28em;
  border-top: 0.07em dashed @button_border;
}

  /*** for following settings line, remember which popover mode name and related line on popover menu
  1) .dt_overlays_none
  2) .dt_overlays_hover
  3) .dt_overlays_hover_extended
  4) .dt_overlays_always
  5) .dt_overlays_always_extended
  6) .dt_overlays_mixed
  7) .dt_overlays_hover_block (also to set culling and preview hover block infos)

  These modes will be ordered on next related lines ***/

/*---------------
  - Bottom area -
  ---------------*/
#thumb-bottom
{
  border : 0.07em solid transparent; /* to not overlay thumb borders */
}

/* Set outside margins in grouped thumbnails about bottom infos area */
.dt_group_left #thumb-bottom
{
  border-left-width: 0.14em;
}
.dt_group_bottom #thumb-bottom
{
  border-bottom-width: 0.14em;
}
.dt_group_right #thumb-bottom
{
  border-right-width: 0.14em;
}

/* Set background on thumbnails hover overlays */
.dt_overlays_hover_extended #thumb-main:hover #thumb-bottom,
.dt_overlays_mixed #thumb-main:hover #thumb-bottom,
.dt_overlays_hover #thumb-main:hover #thumb-bottom
{
  background-image: linear-gradient(rgba(212, 212, 212, 0.7) 0%, rgba(212, 212, 212, 0.7) 90%,rgba(212, 212, 212, 0) 100%);  /* rgb color needs to be set to same color as #thumb-back hover background */
}

/* Set background on block infos in last overlay mode and culling/preview modes shown with mouse hover */
.dt_overlays_hover_block #thumb-image:hover #thumb-bottom
{
  background-image: none;
  background-color: rgba(220, 220, 220, 0.8);
}

/*--------------------
  - Thumbnails infos -
  --------------------*/
/* set some padding for some icons */
#thumb-star,
#thumb-reject,
#thumb-colorlabels
{
  padding : 12.5px; /* percent of the full size */
}

/* Reset color icons to be hidden in following modes, including culling/preview overlays hover block*/
.dt_overlays_none .dt_thumb_btn,
.dt_overlays_none #thumb-main:hover .dt_thumb_btn,
.dt_overlays_hover #thumb-main .dt_thumb_btn,
.dt_overlays_hover_extended #thumb-main .dt_thumb_btn,
.dt_overlays_hover_block #thumb-main .dt_thumb_btn,
#thumb-ext  /* by default, the extension is hidden */
{
  color: transparent;
  background-color: transparent;
}

/* Set default color icons, extension name and text infos on thumbnails and block infos */
.dt_thumb_btn,
.dt_overlays_always_extended #thumb-bottom-label,
.dt_overlays_always #thumb-ext,
.dt_overlays_always_extended #thumb-ext,
.dt_overlays_mixed #thumb-ext
{
  color: shade(@thumbnail_font_color, 1.1);
}

/* Make inactive stars more discrete when not hovered */
.dt_overlays_always #thumb-star,
.dt_overlays_always_extended #thumb-star,
.dt_overlays_mixed #thumb-star,
.dt_overlays_hover_block #thumb-star
{
  color: shade(@thumbnail_font_color, 1.4);
}

/* Set hover and selected color icons, extension name and text infos */
.dt_overlays_hover #thumb-main:hover #thumb-ext,
.dt_overlays_hover #thumb-main:hover .dt_thumb_btn,
.dt_overlays_hover_extended #thumb-main:hover #thumb-ext,
.dt_overlays_hover_extended #thumb-main:hover .dt_thumb_btn,
.dt_overlays_hover_extended #thumb-main:hover #thumb-bottom-label,
.dt_overlays_always #thumb-main:hover #thumb-ext,
.dt_overlays_always #thumb-main:selected #thumb-ext,
.dt_overlays_always #thumb-main:hover .dt_thumb_btn,
.dt_overlays_always #thumb-main:selected .dt_thumb_btn,
.dt_overlays_always_extended #thumb-main:hover #thumb-ext,
.dt_overlays_always_extended #thumb-main:selected #thumb-ext,
.dt_overlays_always_extended #thumb-main:hover .dt_thumb_btn,
.dt_overlays_always_extended #thumb-main:selected .dt_thumb_btn,
.dt_overlays_always_extended #thumb-main:hover #thumb-bottom-label,
.dt_overlays_always_extended #thumb-main:selected #thumb-bottom-label,
.dt_overlays_mixed #thumb-main:hover #thumb-ext,
.dt_overlays_mixed #thumb-main:selected #thumb-ext,
.dt_overlays_mixed #thumb-main:hover .dt_thumb_btn,
.dt_overlays_mixed #thumb-main:selected .dt_thumb_btn,
.dt_overlays_mixed #thumb-main:hover #thumb-bottom-label,
.dt_overlays_hover_block #thumb-image:hover .dt_thumb_btn,
.dt_overlays_hover_block #thumb-image:hover .dt_thumb_btn:active,
.dt_overlays_hover_block #thumb-image:hover #thumb-bottom-label
{
  color: @thumbnail_font_color;
}

.dt_overlays_always_extended.dt_fullview #thumb-main:hover #thumb-bottom-label,
.dt_overlays_always_extended.dt_fullview #thumb-main:selected #thumb-bottom-label
{
  color: shade(@thumbnail_font_color,.75);
}

/* Set red reject icon when visible */
.dt_overlays_hover #thumb-main:hover #thumb-reject:active,
.dt_overlays_hover #thumb-main:hover #thumb-reject:hover,
.dt_overlays_hover_extended #thumb-main:hover #thumb-reject:active,
.dt_overlays_hover_extended #thumb-main:hover #thumb-reject:hover,
.dt_overlays_always #thumb-reject:active,
.dt_overlays_always #thumb-main:hover #thumb-reject:active,
.dt_overlays_always #thumb-main:selected #thumb-reject:active,
.dt_overlays_always #thumb-reject:hover,
.dt_overlays_always #thumb-main:hover #thumb-reject:hover,
.dt_overlays_always_extended #thumb-reject:active,
.dt_overlays_always_extended #thumb-main:hover #thumb-reject:active,
.dt_overlays_always_extended #thumb-main:selected #thumb-reject:active,
.dt_overlays_always_extended #thumb-reject:hover,
.dt_overlays_always_extended #thumb-main:hover #thumb-reject:hover,
.dt_overlays_mixed #thumb-reject:active,
.dt_overlays_mixed #thumb-main:hover #thumb-reject:active,
.dt_overlays_mixed #thumb-main:selected #thumb-reject:active,
.dt_overlays_mixed #thumb-reject:hover,
.dt_overlays_mixed #thumb-main:hover #thumb-reject:hover,
.dt_overlays_hover_block #thumb-image:hover #thumb-reject:active,  /* for overlay hover block, set it on #thumb-image and not #thumb-main here to avoid some glitches when block disappear */
.dt_overlays_hover_block #thumb-image:hover #thumb-reject:hover
{
  color: red;
}

/* Set stars icon when active */
.dt_overlays_hover #thumb-main:hover #thumb-star:active,
.dt_overlays_hover_extended #thumb-main:hover #thumb-star:active,
.dt_overlays_always #thumb-star:active,
.dt_overlays_always_extended #thumb-star:active,
.dt_overlays_mixed #thumb-star:active,
.dt_overlays_hover_block #thumb-image:hover #thumb-star:active
{
  color: @thumbnail_font_color;
  background-color: @thumbnail_font_color;
}

/* Set stars icon hover effect */
.dt_overlays_hover #thumb-main:hover #thumb-star:hover,
.dt_overlays_hover_extended #thumb-main:hover #thumb-star:hover,
.dt_overlays_always #thumb-main:hover #thumb-star:hover,
.dt_overlays_always_extended #thumb-main:hover #thumb-star:hover,
.dt_overlays_mixed #thumb-main:hover #thumb-star:hover,
.dt_overlays_hover_block #thumb-image:hover #thumb-star:hover
{
  color: @thumbnail_star_hover_color;
  background-color: @thumbnail_star_bg_color;
}

/* Set stars icon hover effect on fullview */
.dt_fullview #thumb-main:hover #thumb-star:hover
{
  color: @lighttable_bg_color;
}

/* Set hover group and audio effect */
.dt_overlays_hover #thumb-main:hover #thumb-group-audio:hover,
.dt_overlays_hover_extended #thumb-main:hover #thumb-group-audio:hover,
.dt_overlays_always #thumb-main:hover #thumb-group-audio:hover,
.dt_overlays_always_extended #thumb-main:hover #thumb-group-audio:hover,
.dt_overlays_mixed #thumb-main:hover #thumb-group-audio:hover,
.dt_overlays_hover_block #thumb-image:hover #thumb-group-audio:hover
{
  color: @thumbnail_fg_color;
}

/* Set rejected images */
.dt_thumbnail_rating_6 #thumb-image
{
  color: rgba(0,0,0,0.35); /* only the transparency is used to fade the image drawing */
}

.dt_thumbnail_rating_6#thumb-main:hover #thumb-image
{
  color: rgba(0,0,0,1); /* take back image to normal state by removing transparency when hovering the image */
}

.dt_thumbnail_rating_6 #thumb-back
{
  background-color: shade(@thumbnail_bg_color,0.75); /* also fade the thumb background */
  border: 1px solid shade(@lighttable_bg_color, 0.75); /* this is needed to have appropriate border colors and avoid visible differences in border regardless of not rejected images */
}

.dt_overlays_always .dt_thumbnail_rating_6 #thumb-ext,
.dt_overlays_always .dt_thumbnail_rating_6 #thumb-altered,
.dt_overlays_always_extended .dt_thumbnail_rating_6 #thumb-ext,
.dt_overlays_always_extended .dt_thumbnail_rating_6 #thumb-altered,
.dt_overlays_always_extended .dt_thumbnail_rating_6 #thumb-bottom-label,
.dt_overlays_mixed .dt_thumbnail_rating_6 #thumb-ext,
.dt_overlays_mixed .dt_thumbnail_rating_6 #thumb-altered
{
  color: rgba(0,0,0,0.15); /* add some transparency to infos items in thumbnail */
}

/* Set local copy mark */
#thumb-localcopy
{
  border-top : 0.07em solid @lighttable_bg_color;
  border-right : 0.07em solid @lighttable_bg_color;
  color: @thumbnail_localcopy;
}

.dt_group_right #thumb-localcopy
{
  border-right: 0.07em solid rgb(255, 187, 0); /* to not overlay thumb borders */
}
.dt_group_top #thumb-localcopy
{
  border-top: 0.07em solid rgb(255, 187, 0); /* to not overlay thumb borders */
}

/*---------------------------------------------------------
  - Hide icons on all lighttable modes and duplicate ones -
  ---------------------------------------------------------*/
.dt_fullview #thumb-ext,
.dt_fullview #thumb-main:selected #thumb-ext,
.dt_fullview #thumb-main:hover #thumb-ext,
.dt_duplicate_ui #thumb-main:hover #thumb-group-audio,
.dt_duplicate_ui #thumb-main:hover #thumb-altered,
.dt_duplicate_ui #thumb-group-audio,
.dt_duplicate_ui #thumb-altered,
.dt_overlays_always.dt_fullview #thumb-group-audio,
.dt_overlays_always.dt_fullview #thumb-altered,
.dt_overlays_always_extended.dt_fullview #thumb-group-audio,
.dt_overlays_always_extended.dt_fullview #thumb-altered,
.dt_thumbnail_rating_6 #thumb-star /* needed to hide completely stars in rejected images as they are not needed, except in hover mode if we want to un-reject images */
{
  color: transparent;
}

.dt_overlays_always.dt_fullview #thumb-bottom > *
{
  padding-top: 0.21em;
}

/* Padding of overlays block hover on thumbnail overlay mode */
.dt_overlays_hover_block #thumb-image:hover #thumb-bottom-label
{
  padding-left: 0.28em;
  padding-right: 0.28em;
}

/*-------------------------------------------------
  - Culling and preview modes (dt_fullview class) -
  -----------------------------------------------*/
/* Hide not wanted things on these modes: ext name and thumb_main border */
.dt_fullview #thumb-back,
.dt_fullview #thumb-main:selected #thumb-back,
.dt_fullview #thumb-main:hover #thumb-back,
.dt_fullview #thumb-main:active #thumb-back
{
  background-color: shade(@lighttable_bg_color,1.05);
  border: 1px solid shade(@lighttable_bg_color,.95);
}

/* Set image borders */
.dt_fullview #thumb-main:selected #thumb-image
{
  border: 0.14em solid @plugin_bg_color;
}

.dt_fullview #thumb-main:hover #thumb-image
{
  border: 0.14em solid @thumbnail_hover_bg_color;
}

/* Set how bottom infos are rendered on always and always extended overlays modes in culling and preview modes */
.dt_overlays_always.dt_fullview #thumb-bottom,
.dt_overlays_always_extended.dt_fullview #thumb-bottom
{
  background-color: transparent;
}

/* Set zoom info */
#thumb-zoom-label
{
  color: transparent;
  background-color: transparent;
  border-radius: 0.28em;
  padding: 0.07em 0.42em;
}

/* Set where overlays block infos start on the image */
.dt_overlays_hover_block #thumb-bottom
{
  margin-top: 30px;  /* not real pixels. This is used as a per thousand of the image size and couldn't be scalable */
  margin-left: 0;  /* not real pixels. This is used as a per thousand of the image size and couldn't be scalable */
}

/* Set background color of overlays block infos */
.dt_overlays_hover_block #thumb-image:hover #thumb-zoom-label
{
  color: @bauhaus_fg_hover;
  background-color: rgba(20, 20, 20, 0.5);
}

/* Lighttable toolbox */
#lighttable_layouts_box,
#footer-toolbar .toggle,
#footer-toolbar entry,
#footer-toolbar scale
{
  margin: 0.07em 0.28em;
}

/* splash screen */
#splashscreen
{
    background-color: @grey_10;
    border: 5px solid @grey_60;
}
#splashscreen-header
{
    padding: 0;
    margin: 0;
}
/* make the (nonexistent) title text very small to avoid an obvious bar at top */
#splashscreen-header label
{
    font-size: 1%;
    padding: 0;
    margin: 0;
}
/* style the darktable logo */
#splashscreen-logo
{
}
/* position the darktable version number next to the logo */
#splashscreen-version
{
    margin-top: -35pt;
    margin-right: -110pt;
    font-size: 15pt;
}
/* style the darktable program name */
#splashscreen-program
{
    font-size: 48pt;
    margin: 2pt;
}
/* style the darktable program description */
#splashscreen-description
{
    font-size: 16pt;
    font-family: DejaVu Serif, serif;
    padding: 1em;
    margin: 1em;
}
/* style the "get ready" message */
#splashscreen-prepare
{
    font-size: 14pt;
    font-family: DejaVu Serif, serif;
}
/* style the separating bar - without a style, it won't appear or take space! */
#splashscreen-separator
{
    border: 2px solid @grey_80;
    margin-top: 15pt;
    margin-bottom: 15pt;
}
/* style the progress message */
#splashscreen-progress
{
}
/* style the time remaining */
#splashscreen-remaining
{
    color: yellow;
    font-size: 150%;
}

/* style the message on the exit screen */
#exitscreen-message
{
    font-size: 15pt;
    margin-bottom: 10pt;
}

/* ??? TO BE REMOVED ON DT 5.0 : script_manager power button */
button#pb_off
{
  opacity: 0.5;
}
button#pb_on
{
  opacity: 1.0;
}
label#pb_label
{
  padding-left: .5em;
}

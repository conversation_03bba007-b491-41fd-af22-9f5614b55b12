{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"version": {"type": "integer", "minimum": 0, "maximum": 0, "enum": [0]}, "noiseprofiles": {"type": "array", "minItems": 1, "uniqueItems": true, "items": {"type": "object", "properties": {"maker": {"type": "string", "pattern": "^[a-zA-Z ]+$", "enum": ["Canon", "DJI", "Fujifilm", "Hasselblad", "LGE", "Le<PERSON>", "<PERSON><PERSON><PERSON>", "Nikon", "Olympus", "OM System", "OnePlus", "Panasonic", "Pentax", "Phase One", "Raspberrypi", "<PERSON><PERSON>", "Samsung", "Sony", "YI TECHNOLOGY"]}, "models": {"type": "array", "uniqueItems": true, "minItems": 1, "items": {"type": "object", "properties": {"comment": {"type": "string"}, "model": {"type": "string", "pattern": "^[a-zA-Z0-9_\\- ()\\*]+$", "minLength": 2, "maxLength": 30}, "profiles": {"type": "array", "uniqueItems": true, "minItems": 4, "items": {"type": "object", "properties": {"name": {"type": "string", "pattern": "^[a-zA-Z0-9_\\- ()\\*]+ iso [0-9]{2,7}$", "minLength": 9, "maxLength": 38}, "iso": {"type": "integer", "minimum": 1, "maximum": 2048000}, "a": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3}, "b": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3}, "skip": {"type": "boolean", "default": false, "enum": [true]}}, "required": ["name", "iso", "a", "b"], "additionalProperties": false}}}, "required": ["comment", "model", "profiles"], "additionalProperties": false}}}, "required": ["maker", "models"], "additionalProperties": false}}}, "required": ["version", "noiseprofiles"], "additionalProperties": false}
/*
    This file is part of darktable,
    copyright (c) 2019 Pascal Obry

    darktable is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    darktable is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    ME<PERSON><PERSON>NTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with darktable.  If not, see <http://www.gnu.org/licenses/>.
*/

@import url("darktable.css");

/* set first default round icon */
.dt_icon
{
    background-image: url('../pixmaps/plugins/darkroom/default.svg');
    background-size: contain;
    background-repeat: no-repeat;
    margin-top: 0.14em;
    margin-left: 0.30em;
    min-height: 1.1em;
    min-width: 1.1em;
}

/* then set modules with proper icon */
#iop-panel-icon-ashift
{
    background-image: url('../pixmaps/plugins/darkroom/ashift.svg');
}

#iop-panel-icon-atrous
{
    background-image: url('../pixmaps/plugins/darkroom/atrous.svg');
}

#iop-panel-icon-basecurve
{
    background-image: url('../pixmaps/plugins/darkroom/basecurve.svg');
}

#iop-panel-icon-bilateral
{
    background-image: url('../pixmaps/plugins/darkroom/bilateral.svg');
}

#iop-panel-icon-bloom
{
    background-image: url('../pixmaps/plugins/darkroom/bloom.svg');
}

#iop-panel-icon-borders
{
    background-image: url('../pixmaps/plugins/darkroom/borders.svg');
}

#iop-panel-icon-cacorrect
{
    background-image: url('../pixmaps/plugins/darkroom/cacorrect.svg');
}

#iop-panel-icon-cacorrectrgb
{
    background-image: url('../pixmaps/plugins/darkroom/cacorrect.svg');
}

#iop-panel-icon-channelmixer
{
    background-image: url('../pixmaps/plugins/darkroom/channelmixer.svg');
}

#iop-panel-icon-clahe
{
    background-image: url('../pixmaps/plugins/darkroom/clahe.svg');
}

#iop-panel-icon-clipping
{
    background-image: url('../pixmaps/plugins/darkroom/clipping.svg');
}

#iop-panel-icon-crop
{
    background-image: url('../pixmaps/plugins/darkroom/clipping.svg');
}

#iop-panel-icon-colisa
{
    background-image: url('../pixmaps/plugins/darkroom/colisa.svg');
}

#iop-panel-icon-colorcorrection
{
    background-image: url('../pixmaps/plugins/darkroom/colorcorrection.svg');
}

#iop-panel-icon-colorin
{
    background-image: url('../pixmaps/plugins/darkroom/colorin.svg');
}

#iop-panel-icon-colormapping
{
    background-image: url('../pixmaps/plugins/darkroom/colormapping.svg');
}

#iop-panel-icon-colorout
{
    background-image: url('../pixmaps/plugins/darkroom/colorout.svg');
}

#iop-panel-icon-colorreconstruct
{
    background-image: url('../pixmaps/plugins/darkroom/colorreconstruct.svg');
}

#iop-panel-icon-colortransfer
{
    background-image: url('../pixmaps/plugins/darkroom/colortransfer.svg');
}

#iop-panel-icon-colorzones
{
    background-image: url('../pixmaps/plugins/darkroom/colorzones.svg');
}

#iop-panel-icon-demosaic
{
    background-image: url('../pixmaps/plugins/darkroom/demosaic.svg');
}

#iop-panel-icon-dither
{
    background-image: url('../pixmaps/plugins/darkroom/dither.svg');
}

#iop-panel-icon-exposure
{
    background-image: url('../pixmaps/plugins/darkroom/exposure.svg');
}

#iop-panel-icon-flip
{
    background-image: url('../pixmaps/plugins/darkroom/flip.svg');
}

#iop-panel-icon-gamma
{
    background-image: url('../pixmaps/plugins/darkroom/profile_gamma.svg');
}

#iop-panel-icon-graduatednd
{
    background-image: url('../pixmaps/plugins/darkroom/graduatednd.svg');
}

#iop-panel-icon-grain
{
    background-image: url('../pixmaps/plugins/darkroom/grain.svg');
}

#iop-panel-icon-hazeremoval
{
    background-image: url('../pixmaps/plugins/darkroom/hazeremoval.svg');
}

#iop-panel-icon-highlights
{
    background-image: url('../pixmaps/plugins/darkroom/highlights.svg');
}

#iop-panel-icon-highpass
{
    background-image: url('../pixmaps/plugins/darkroom/highpass.svg');
}

#iop-panel-icon-hotpixels
{
    background-image: url('../pixmaps/plugins/darkroom/hotpixels.svg');
}

#iop-panel-icon-invert
{
    background-image: url('../pixmaps/plugins/darkroom/invert.svg');
}

#iop-panel-icon-lens
{
    background-image: url('../pixmaps/plugins/darkroom/lens.svg');
}

#iop-panel-icon-levels
{
    background-image: url('../pixmaps/plugins/darkroom/levels.svg');
}

#iop-panel-icon-liquify
{
    background-image: url('../pixmaps/plugins/darkroom/liquify.svg');
}

#iop-panel-icon-lowlight
{
    background-image: url('../pixmaps/plugins/darkroom/lowlight.svg');
}

#iop-panel-icon-lowpass
{
    background-image: url('../pixmaps/plugins/darkroom/lowpass.svg');
}

#iop-panel-icon-monochrome
{
    background-image: url('../pixmaps/plugins/darkroom/monochrome.svg');
}

#iop-panel-icon-negadoctor
{
    background-image: url('../pixmaps/plugins/darkroom/invert.svg');
}

#iop-panel-icon-nlmeans
{
    background-image: url('../pixmaps/plugins/darkroom/nlmeans.svg');
}

#iop-panel-icon-overexposed
{
    background-image: url('../pixmaps/plugins/darkroom/overexposed.svg');
}

#iop-panel-icon-rawdenoise
{
    background-image: url('../pixmaps/plugins/darkroom/rawdenoise.svg');
}

#iop-panel-icon-rawimport
{
    background-image: url('../pixmaps/plugins/darkroom/rawimport.svg');
}

#iop-panel-icon-rawprepare
{
    background-image: url('../pixmaps/plugins/darkroom/rawprepare.svg');
}

#iop-panel-icon-relight
{
    background-image: url('../pixmaps/plugins/darkroom/relight.svg');
}

#iop-panel-icon-shadhi
{
    background-image: url('../pixmaps/plugins/darkroom/shadhi.svg');
}

#iop-panel-icon-sharpen
{
    background-image: url('../pixmaps/plugins/darkroom/sharpen.svg');
}

#iop-panel-icon-soften
{
    background-image: url('../pixmaps/plugins/darkroom/soften.svg');
}

#iop-panel-icon-splittoning
{
    background-image: url('../pixmaps/plugins/darkroom/splittoning.svg');
}

#iop-panel-icon-spots
{
    background-image: url('../pixmaps/plugins/darkroom/spots.svg');
}

#iop-panel-icon-temperature
{
    background-image: url('../pixmaps/plugins/darkroom/temperature.svg');
}

#iop-panel-icon-template
{
    background-image: url('../pixmaps/plugins/darkroom/template.svg');
}

#iop-panel-icon-tonecurve
{
    background-image: url('../pixmaps/plugins/darkroom/tonecurve.svg');
}

#iop-panel-icon-tonemap
{
    background-image: url('../pixmaps/plugins/darkroom/tonemap.svg');
}

#iop-panel-icon-velvia
{
    background-image: url('../pixmaps/plugins/darkroom/velvia.svg');
}

#iop-panel-icon-vignette
{
    background-image: url('../pixmaps/plugins/darkroom/vignette.svg');
}

#iop-panel-icon-watermark
{
    background-image: url('../pixmaps/plugins/darkroom/watermark.svg');
}

#iop-panel-icon-zonesystem
{
    background-image: url('../pixmaps/plugins/darkroom/zonesystem.svg');
}

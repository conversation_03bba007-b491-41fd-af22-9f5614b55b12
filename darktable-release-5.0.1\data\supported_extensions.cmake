#
# file extensions supported by this build. this is used both inside dt as well as for the Windows installation.
#
set(DT_SUPPORTED_EXTENSIONS
    "3fr"
    "ari"
    "arw"
    "bay"
    "bmq"
    "cap"
    "cine"
    "cr2"
    "crw"
    "cs1"
    "dc2"
    "dcr"
    "dng"
    "erf"
    "fff"
    "gpr"
    "hdr"
    "ia"
    "iiq"
    "jfif"
    "jpeg"
    "jpg"
    "k25"
    "kc2"
    "kdc"
    "mdc"
    "mef"
    "mos"
    "mrw"
    "nef"
    "nrw"
    "orf"
    "ori"
    "pbm"
    "pef"
    "pfm"
    "pgm"
    "png"
    "pnm"
    "ppm"
    "pxn"
    "qoi"
    "qtk"
    "raf"
    "raw"
    "rdc"
    "rw2"
    "rwl"
    "sr2"
    "srf"
    "srw"
    "sti"
    "tif"
    "tiff"
    "x3f"
    CACHE INTERNAL ""
)

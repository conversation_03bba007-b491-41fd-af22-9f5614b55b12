if(CMAKE_SYSTEM_NAME STREQUAL "SunOS")
  set(MARCH "-msse4.2")
elseif(NOT BINARY_PACKAGE_BUILD AND (NOT APPLE OR CMAKE_C_COMPILER_ID STREQUAL "AppleClang"))
  MESSAGE("-- Checking for -march=native support")
  CHECK_C_COMPILER_FLAG("-march=native" MARCHNATIVE)
  if(MARCHNATIVE)
    set(MARCH "-march=native")
    add_definitions("-DNATIVE_ARCH")
  else()
    MESSAGE("-- Checking for -mtune=native support")
    CHECK_C_COMPILER_FLAG("-mtune=native" MTUNENATIVE)
    if(MTUNENATIVE)
      set(MARCH "-mtune=native")
    else()
      MESSAGE("-- Checking for -mtune=generic support")
      CHECK_C_COMPILER_FLAG("-mtune=generic" MTUNEGENERIC)
      if(MTUNEGENERIC)
        set(MARCH "-mtune=generic")
      else()
        message(WARNING "Do not know which -march/-mtune to pass! Resulting binaries may be broken!")
      endif()
    endif()
  endif()
else()
  MESSAGE("-- Checking for -mtune=generic support")
  CHECK_C_COMPILER_FLAG("-mtune=generic" MTUNEGENERIC)
  if(MTUNEGENERIC)
    set(MARCH "-mtune=generic")
  else()
    message(WARNING "Do not know which -march/-mtune to pass! Resulting binaries may be broken!")
  endif()
endif()

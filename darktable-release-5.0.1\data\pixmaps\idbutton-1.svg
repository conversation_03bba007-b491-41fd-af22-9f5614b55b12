<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="65"
   height="50.117401"
   id="svg2"
   version="1.1"
   inkscape:version="0.92.3 (2405546, 2018-03-11)"
   sodipodi:docname="idbutton-1.svg"
   inkscape:export-filename="/home/<USER>/programming/darktable/darktable/data/pixmaps/idbutton-3.png"
   inkscape:export-xdpi="95.775116"
   inkscape:export-ydpi="95.775116">
  <defs
     id="defs4">
    <linearGradient
       id="linearGradient4206">
      <stop
         style="stop-color:#ffffff;stop-opacity:1;"
         offset="0"
         id="stop4208" />
      <stop
         style="stop-color:#b0b0b0;stop-opacity:1;"
         offset="1"
         id="stop4210" />
    </linearGradient>
    <linearGradient
       id="linearGradient4209">
      <stop
         style="stop-color:#ffffff;stop-opacity:1;"
         offset="0"
         id="stop4211" />
      <stop
         style="stop-color:#ffffff;stop-opacity:0;"
         offset="1"
         id="stop4213" />
    </linearGradient>
    <linearGradient
       id="linearGradient4163">
      <stop
         style="stop-color:#ff7004;stop-opacity:1;"
         offset="0"
         id="stop4165" />
      <stop
         style="stop-color:#ba4704;stop-opacity:1;"
         offset="1"
         id="stop4167" />
    </linearGradient>
    <linearGradient
       id="linearGradient4153">
      <stop
         style="stop-color:#d70016;stop-opacity:1;"
         offset="0"
         id="stop4155" />
      <stop
         style="stop-color:#8d0016;stop-opacity:1;"
         offset="1"
         id="stop4157" />
    </linearGradient>
    <linearGradient
       id="linearGradient4143">
      <stop
         style="stop-color:#d000d0;stop-opacity:1;"
         offset="0"
         id="stop4145" />
      <stop
         style="stop-color:#760076;stop-opacity:1;"
         offset="1"
         id="stop4147" />
    </linearGradient>
    <linearGradient
       id="linearGradient4133">
      <stop
         style="stop-color:#1476e6;stop-opacity:1;"
         offset="0"
         id="stop4135" />
      <stop
         style="stop-color:#143e9a;stop-opacity:1;"
         offset="1"
         id="stop4137" />
    </linearGradient>
    <linearGradient
       id="linearGradient4123">
      <stop
         style="stop-color:#17b414;stop-opacity:1;"
         offset="0"
         id="stop4125" />
      <stop
         style="stop-color:#176314;stop-opacity:1;"
         offset="1"
         id="stop4127" />
    </linearGradient>
    <linearGradient
       id="linearGradient4105">
      <stop
         style="stop-color:#f7f10e;stop-opacity:1;"
         offset="0"
         id="stop4107" />
      <stop
         style="stop-color:#c58907;stop-opacity:1;"
         offset="1"
         id="stop4109" />
    </linearGradient>
    <filter
       inkscape:collect="always"
       id="filter4107"
       style="color-interpolation-filters:sRGB">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="9.0000227"
         id="feGaussianBlur4109" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter3902">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="9"
         id="feGaussianBlur3904" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4163"
       id="linearGradient3885"
       gradientUnits="userSpaceOnUse"
       x1="372.85916"
       y1="593.388"
       x2="318.29666"
       y2="726.388"
       gradientTransform="matrix(0.08253776,0,0,0.08253776,-2.5976102,428.43719)" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4105"
       id="linearGradient3907"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.04126888,0.0714798,-0.0714798,0.04126888,64.342166,442.86516)"
       x1="277.62738"
       y1="637.05853"
       x2="454.04785"
       y2="683.48932" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4123"
       id="linearGradient3911"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(-0.04126888,0.0714798,-0.0714798,-0.04126888,85.317058,508.0507)"
       x1="253.83762"
       y1="725.2688"
       x2="401.18628"
       y2="612.48419" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4133"
       id="linearGradient3915"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(-0.08253776,0,0,-0.08253776,39.352174,558.80826)"
       x1="326.12479"
       y1="701.76617"
       x2="342.12479"
       y2="581.76617" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4143"
       id="linearGradient3919"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(-0.04126888,-0.0714798,0.0714798,-0.04126888,-27.587603,544.38028)"
       x1="456.97604"
       y1="694.41754"
       x2="273.62738"
       y2="643.98676" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4153"
       id="linearGradient3923"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.04126888,-0.0714798,0.0714798,0.04126888,-48.562496,479.19475)"
       x1="432.1145"
       y1="650.05341"
       x2="263.69403"
       y2="710.34058" />
    <filter
       inkscape:collect="always"
       id="filter3999"
       x="-0.10182051"
       width="1.2036411"
       y="-0.14085203"
       height="1.2817041">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="9.2672575"
         id="feGaussianBlur4001" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter4007"
       x="-0.1193153"
       width="1.2386307"
       y="-0.10704988"
       height="1.2140998">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="9.2672575"
         id="feGaussianBlur4009" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter4011"
       x="-0.10182051"
       width="1.2036411"
       y="-0.14085203"
       height="1.2817041">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="9.2672575"
         id="feGaussianBlur4013" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter4015"
       x="-0.12529333"
       width="1.2505867"
       y="-0.11026258"
       height="1.2205251">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="9.2672575"
         id="feGaussianBlur4017" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter4019"
       x="-0.1193153"
       width="1.2386307"
       y="-0.10704988"
       height="1.2140998">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="9.2672575"
         id="feGaussianBlur4021" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient4206"
       id="linearGradient4212"
       x1="256"
       y1="224"
       x2="256"
       y2="288"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
    <filter
       inkscape:collect="always"
       id="filter4245"
       x="-0.12561549"
       width="1.251231"
       y="-0.11054609"
       height="1.2210922">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="9.2910855"
         id="feGaussianBlur4247" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter4257"
       x="-0.1176"
       width="1.2352"
       y="-0.1176"
       height="1.2352">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="3.136"
         id="feGaussianBlur4259" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter4308">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="4.8309114"
         id="feGaussianBlur4310" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter4312">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="4.8192188"
         id="feGaussianBlur4314" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter4320">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="4.8252523"
         id="feGaussianBlur4322" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter4328">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="5.0573438"
         id="feGaussianBlur4330" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter4336">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="5.056875"
         id="feGaussianBlur4338" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter4344">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="4.8220313"
         id="feGaussianBlur4346" />
    </filter>
    <filter
       inkscape:collect="always"
       id="filter4348">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="1.7582689"
         id="feGaussianBlur4350" />
    </filter>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath4264">
      <path
         sodipodi:nodetypes="cscscscccc"
         inkscape:connector-curvature="0"
         id="path4266"
         d="m 225.68807,-8.1834862 c 46.82418,-37.0701688 63.09746,-57.4854428 115.72076,-70.1817298 45.13773,-10.890263 85.07319,-20.300875 167.07511,-8.092376 85.89123,40.568757 231.15425,206.898572 214.07413,364.376932 -9.07121,83.63638 -50.73692,119.43206 -80.82534,162.90063 -28.00317,-30.51243 -39.15603,-63.43205 -50.45871,-96.33027 -5.51756,-16.05971 10.56461,-54.56142 33.96323,-72.9741 -23.30206,-40.62781 -43.91409,-75.38662 -68.81699,-113.60749 -2.6654,20.99162 -15.69025,23.12123 -29.81476,43.08272 z"
         style="fill:#980101;stroke:none" />
    </clipPath>
    <clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath4283">
      <path
         sodipodi:nodetypes="cscscscccc"
         inkscape:connector-curvature="0"
         id="path4285"
         d="m 225.68807,-8.1834862 c 46.82418,-37.0701688 63.09746,-57.4854428 115.72076,-70.1817298 45.13773,-10.890263 85.07319,-20.300875 167.07511,-8.092376 85.89123,40.568757 231.15425,206.898572 214.07413,364.376932 -9.07121,83.63638 -50.73692,119.43206 -80.82534,162.90063 -28.00317,-30.51243 -39.15603,-63.43205 -50.45871,-96.33027 -5.51756,-16.05971 10.56461,-54.56142 33.96323,-72.9741 -23.30206,-40.62781 -43.91409,-75.38662 -68.81699,-113.60749 -2.6654,20.99162 -15.69025,23.12123 -29.81476,43.08272 z"
         style="fill:#980101;stroke:none" />
    </clipPath>
    <linearGradient
       gradientTransform="matrix(0.07662222,0,0,0.07662222,1.7903898,0.0283495)"
       inkscape:collect="always"
       xlink:href="#linearGradient4362"
       id="linearGradient4368"
       x1="351.43207"
       y1="47.579136"
       x2="447.5986"
       y2="92.126862"
       gradientUnits="userSpaceOnUse" />
    <linearGradient
       inkscape:collect="always"
       id="linearGradient4362">
      <stop
         style="stop-color:#f6f6f6;stop-opacity:1"
         offset="0"
         id="stop4364" />
      <stop
         style="stop-color:#dddddf;stop-opacity:1"
         offset="1"
         id="stop4366" />
    </linearGradient>
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="7.8022811"
     inkscape:cx="30.914205"
     inkscape:cy="26.49369"
     inkscape:document-units="px"
     inkscape:current-layer="layer2"
     showgrid="false"
     inkscape:window-width="1920"
     inkscape:window-height="1053"
     inkscape:window-x="0"
     inkscape:window-y="27"
     inkscape:window-maximized="1"
     showguides="false"
     inkscape:guide-bbox="true"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0"
     inkscape:snap-page="true"
     inkscape:snap-object-midpoints="true">
    <inkscape:grid
       type="xygrid"
       id="grid3906"
       empspacing="4"
       visible="true"
       enabled="true"
       snapvisiblegridlinesonly="true"
       originx="2.9999996"
       originy="3.0000018"
       spacingx="1"
       spacingy="1" />
    <sodipodi:guide
       orientation="1,0"
       position="259,515.00001"
       id="guide4171"
       inkscape:locked="false" />
    <sodipodi:guide
       orientation="0,1"
       position="2.9999996,259.00001"
       id="guide4173"
       inkscape:locked="false" />
  </sodipodi:namedview>
  <metadata
     id="metadata7">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title />
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:groupmode="layer"
     id="layer12"
     inkscape:label="Spooky">
    <path
       style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:url(#linearGradient4368);fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.5669291;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
       d="M 17.827697,16.56883 C 20.921802,11.19599 23.530527,-0.7526945 33.323203,1.2900447 44.595478,3.6414256 33.767538,16.289465 36.465648,24.370764 Z"
       id="path3517"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="cscc" />
    <ellipse
       style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#000000;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.5669291;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
       id="path3521"
       cx="28.916307"
       cy="0.80483389"
       rx="1.3792"
       ry="1.8772444"
       transform="matrix(0.97440259,0.22481011,-0.22481011,0.97440259,0,0)" />
    <circle
       style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#ffffff;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.5669291;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
       id="path3532"
       cx="27.956879"
       cy="6.4803762"
       r="0.30648887" />
    <ellipse
       transform="matrix(0.91656746,0.39988011,-0.39988011,0.91656746,0,0)"
       ry="1.8772444"
       rx="1.3791999"
       cy="-4.906652"
       cx="34.254627"
       id="ellipse3534"
       style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#000000;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.5669291;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate" />
    <circle
       r="0.3064889"
       cy="2.0820785"
       cx="34.202518"
       id="circle3536"
       style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#ffffff;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.5669291;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
       transform="matrix(0.98300279,0.1835906,-0.1835906,0.98300279,0,0)" />
    <circle
       style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#f2f2c3;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.56699997;stroke-linecap:butt;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
       id="path5861"
       cx="51.907898"
       cy="9.8727598"
       r="9.6125736" />
    <path
       sodipodi:nodetypes="ccccccscccccccc"
       inkscape:connector-curvature="0"
       d="M 11.578548,8.4693327 C 11.329306,7.7529479 10.454834,7.7068382 10.213754,8.2273574 10.200861,7.8383686 9.7439602,7.9762894 9.3237156,8.140549 9.5820375,7.9661975 10.312265,7.4041352 10.353931,7.053774 c 0,0 0.788885,0.4759274 1.537788,0.4276845 l 0.01241,-0.639591 c 0.0628,0.2087167 0.135124,0.1163471 0.212742,0.088179 0.07762,-0.028169 0.192354,-0.00369 0.106674,-0.2040963 l 0.419721,0.4827682 C 13.248807,6.7654285 13.54885,5.894325 13.54885,5.894325 c 0.256669,0.242094 1.177414,0.2050112 1.487432,0.1731103 -0.42778,0.1434919 -0.866794,0.3307022 -0.627212,0.6374241 -0.5188,-0.2447552 -1.160171,0.35146 -0.891954,1.0609587 C 13.022959,7.6085564 12.731318,7.8423599 12.750919,8.6771904 12.230544,8.0240925 11.856833,8.0317478 11.578548,8.4693327 Z"
       style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#000000;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.5669291;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
       id="path4392" />
    <path
       sodipodi:nodetypes="ccccccscccccccc"
       inkscape:connector-curvature="0"
       d="m 51.755072,31.622562 c -0.421832,-1.512231 -2.222274,-1.72105 -2.787881,-0.677104 0.02388,-0.805222 -0.937865,-0.579651 -1.827312,-0.294914 0.556269,-0.326614 2.137723,-1.392853 2.269294,-2.111196 0,0 1.567822,1.085595 3.121123,1.083199 L 52.639,28.302931 c 0.102611,0.439308 0.26402,0.257891 0.428017,0.209784 0.163997,-0.04811 0.397831,0.01737 0.246866,-0.407756 l 0.804336,1.051782 c 1.308454,-0.837078 2.041394,-2.597583 2.041394,-2.597583 0.498771,0.533438 2.405605,0.576413 3.050163,0.550774 -0.902316,0.240863 -1.833517,0.570575 -1.378437,1.235296 -1.039919,-0.572978 -2.442253,0.575352 -1.98033,2.075822 -1.000376,-0.389039 -1.633194,0.05606 -1.701123,1.783151 -0.990138,-1.416712 -1.763123,-1.449432 -2.394814,-0.581639 z"
       style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#000000;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.5669291;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
       id="path4394" />
    <path
       id="path4396"
       style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#000000;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.5669291;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
       d="M 53.481677,9.1811751 C 52.442665,8.0042133 50.731345,8.6013556 50.677543,9.7874587 50.347927,9.0524038 49.580781,9.6747616 48.904501,10.318839 c 0.358183,-0.5364843 1.316454,-2.1855984 1.121632,-2.8894256 0,0 1.884289,0.2933221 3.281102,-0.3861354 l -0.47758,-1.2349571 c 0.283899,0.3506026 0.35005,0.11696 0.476658,0.00216 0.12661,-0.1148023 0.365592,-0.1578402 0.04436,-0.4745942 l 1.182463,0.5958045 C 55.345655,4.6078438 55.237597,2.7039264 55.237597,2.7039264 55.919054,2.966497 57.653804,2.1737162 58.22268,1.8695896 57.515687,2.479795 56.821442,3.1825517 57.520826,3.5823197 56.335133,3.5201278 55.573852,5.1650145 56.643814,6.3139126 55.573912,6.4000092 55.198504,7.0765021 55.890452,8.6603754 54.381657,7.8171777 53.67176,8.1247845 53.481677,9.1811781 Z"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="ccccccscccccccc" />
    <path
       id="path4398"
       style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#000000;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.5669291;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
       d="M 46.039037,24.378894 C 45.789795,23.66251 44.915323,23.6164 44.674243,24.136919 c -0.01289,-0.388989 -0.469794,-0.251068 -0.890038,-0.08681 0.258322,-0.174352 0.988549,-0.736414 1.030215,-1.086775 0,0 0.788885,0.475927 1.537788,0.427684 l 0.01241,-0.639591 c 0.06279,0.208717 0.135124,0.116347 0.212741,0.08818 0.07762,-0.02817 0.192354,-0.0037 0.106674,-0.204096 l 0.419721,0.482768 c 0.605539,-0.44329 0.905582,-1.314393 0.905582,-1.314393 0.256669,0.242094 1.177414,0.205011 1.487432,0.17311 -0.42778,0.143492 -0.866794,0.330702 -0.627212,0.637424 -0.5188,-0.244755 -1.160171,0.35146 -0.891953,1.060959 -0.494158,-0.157262 -0.785799,0.07654 -0.766198,0.911372 -0.520375,-0.653098 -0.894086,-0.645442 -1.172371,-0.207858 z"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="ccccccscccccccc" />
    <g
       id="g5863"
       transform="translate(2.6915206,-2.0506823)">
      <path
         id="path4388"
         style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#000000;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.5669291;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
         d="m 44.354408,17.22166 c 0.912515,-2.161303 -0.999603,-4.079557 -2.631897,-3.384159 0.776598,-0.919793 -0.55824,-1.550096 -1.863507,-2.043958 0.954428,0.135098 3.795851,0.358108 4.617653,-0.359926 0,0 0.824801,2.727683 2.644177,4.169067 l 1.354085,-1.4427 c -0.288409,0.609331 0.06909,0.547171 0.305673,0.643368 0.236581,0.0962 0.44926,0.390203 0.667912,-0.247493 l -0.03693,1.978273 c 2.308987,0.237274 4.803261,-1.140821 4.803261,-1.140821 0.08753,1.087782 2.278303,2.910941 3.056184,3.480229 -1.279527,-0.557156 -2.675454,-1.037226 -2.7611,0.16352 -0.683835,-1.637173 -3.392034,-1.597612 -4.246716,0.587206 -0.808591,-1.385223 -1.962734,-1.45288 -3.647968,0.504416 0.158866,-2.577942 -0.714998,-3.334904 -2.260822,-2.907022 z"
         inkscape:connector-curvature="0"
         sodipodi:nodetypes="ccccccscccccccc" />
      <circle
         r="0.12625407"
         cy="15.419545"
         cx="48.062378"
         id="path4400"
         style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#fd3301;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.5669291;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate" />
      <circle
         style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#fd3301;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.5669291;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
         id="circle4402"
         cx="48.826279"
         cy="15.724874"
         r="0.12625407" />
    </g>
    <path
       sodipodi:nodetypes="ccccccscccccccc"
       inkscape:connector-curvature="0"
       d="m 45.91087,6.3072557 c -0.249242,-0.716384 -1.123714,-0.762494 -1.364794,-0.241975 -0.01289,-0.388989 -0.469794,-0.251068 -0.890038,-0.08681 0.258322,-0.174352 0.988549,-0.736414 1.030215,-1.086775 0,0 0.788885,0.475927 1.537788,0.427684 l 0.01241,-0.639591 c 0.06279,0.208717 0.135124,0.116347 0.212741,0.08818 0.07762,-0.02817 0.192354,-0.0037 0.106674,-0.204096 l 0.419721,0.482768 c 0.605539,-0.44329 0.905582,-1.314393 0.905582,-1.314393 0.256669,0.242094 1.177414,0.205011 1.487432,0.17311 -0.42778,0.143492 -0.866794,0.330702 -0.627212,0.637424 -0.5188,-0.244755 -1.160171,0.35146 -0.891953,1.060959 -0.494158,-0.157262 -0.785799,0.07654 -0.766198,0.911372 -0.520375,-0.653098 -0.894086,-0.645442 -1.172371,-0.207858 z"
       style="color:#000000;text-decoration:none;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;opacity:1;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;fill:#000000;fill-opacity:1;fill-rule:evenodd;stroke:none;stroke-width:0.5669291;stroke-linecap:round;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate"
       id="path5868" />
  </g>
  <g
     inkscape:groupmode="layer"
     id="layer3"
     inkscape:label="Backdrop"
     style="display:inline"
     transform="translate(2.9999996,-464.88261)">
    <path
       sodipodi:type="arc"
       style="opacity:0.95;fill:#1a1a1a;fill-opacity:1;stroke:none;filter:url(#filter4107)"
       id="path3947"
       sodipodi:cx="285"
       sodipodi:cy="287"
       sodipodi:rx="225"
       sodipodi:ry="225"
       d="M 510,287 A 225,225 0 0 1 288.48211,511.97305 225,225 0 0 1 60.107778,293.96338 225,225 0 0 1 274.55701,62.242477 225,225 0 0 1 509.56899,273.07991"
       transform="matrix(0.07886942,0,0,0.07886942,-4.1830409,471.06974)"
       sodipodi:start="0"
       sodipodi:end="6.2212787"
       sodipodi:open="true" />
    <path
       sodipodi:type="arc"
       style="display:inline;opacity:0.95;fill:#1a1a1a;fill-opacity:1;stroke:none;filter:url(#filter3902)"
       id="path3892"
       sodipodi:cx="285"
       sodipodi:cy="287"
       sodipodi:rx="225"
       sodipodi:ry="225"
       d="M 510,287 A 225,225 0 0 1 288.48211,511.97305 225,225 0 0 1 60.107778,293.96338 225,225 0 0 1 274.55701,62.242477 225,225 0 0 1 509.56899,273.07991"
       transform="matrix(0.07886942,0,0,0.07886942,-4.1830409,471.06974)"
       sodipodi:start="0"
       sodipodi:end="6.2212787"
       sodipodi:open="true" />
    <path
       sodipodi:open="true"
       sodipodi:end="6.2212787"
       sodipodi:start="0"
       d="M 36.040363,493.70526 A 17.745619,17.745619 0 0 1 18.569376,511.44875 17.745619,17.745619 0 0 1 0.55762609,494.25446 17.745619,17.745619 0 0 1 17.471112,475.97877 17.745619,17.745619 0 0 1 36.00637,492.60739"
       sodipodi:ry="17.745619"
       sodipodi:rx="17.745619"
       sodipodi:cy="493.70526"
       sodipodi:cx="18.294744"
       id="path3021"
       style="display:inline;opacity:0.95;fill:#1a1a1a;fill-opacity:1;stroke:none"
       sodipodi:type="arc" />
  </g>
  <g
     inkscape:groupmode="layer"
     id="layer9"
     inkscape:label="Blades Shadows New"
     style="display:inline"
     transform="translate(2.9999996,-464.88261)">
    <path
       inkscape:transform-center-y="-11.91665"
       inkscape:transform-center-x="-8.3324442"
       inkscape:connector-curvature="0"
       id="path3951"
       d="m 303.20313,32.6687 -55.46875,157.90625 218.4375,-36.75 C 433.81752,91.69016 374.21313,46.03153 303.20313,32.6687 Z"
       style="display:inline;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;filter:url(#filter3999)"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
    <path
       style="display:inline;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;filter:url(#filter4245)"
       d="m 473.01214,185.21346 -164.4852,30.91578 141.04518,170.79742 c 37.63313,-59.08709 47.37247,-133.53532 23.44002,-201.7132 z"
       id="path3953"
       inkscape:connector-curvature="0"
       inkscape:transform-center-x="-11.661291"
       inkscape:transform-center-y="2.4819169"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
    <path
       inkscape:transform-center-y="10.683501"
       inkscape:transform-center-x="-6.3227731"
       inkscape:connector-curvature="0"
       id="path3955"
       d="M 425.80901,408.54475 316.79256,281.55428 239.40024,489.1017 c 69.98749,3.0477 139.33122,-25.7419 186.40877,-80.55695 z"
       style="display:inline;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;filter:url(#filter4007)"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
    <path
       style="display:inline;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;filter:url(#filter4011)"
       d="M 208.79687,479.33129 264.26562,321.42504 45.828123,358.17503 c 32.354359,62.1348 91.958747,107.79343 162.968747,121.15626 z"
       id="path3957"
       inkscape:connector-curvature="0"
       inkscape:transform-center-x="8.3324463"
       inkscape:transform-center-y="11.916651"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
    <path
       inkscape:transform-center-y="-2.4819148"
       inkscape:transform-center-x="11.661293"
       inkscape:connector-curvature="0"
       id="path3959"
       d="M 38.987861,326.78653 203.47306,295.87075 62.427886,125.07333 C 24.794751,184.16042 15.055411,258.60865 38.987861,326.78653 Z"
       style="display:inline;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;filter:url(#filter4015)"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
    <path
       style="display:inline;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;filter:url(#filter4019)"
       d="M 86.190993,103.45524 195.20744,230.44571 272.59976,22.898294 C 202.61227,19.850588 133.26854,48.640187 86.190993,103.45524 Z"
       id="path3961"
       inkscape:connector-curvature="0"
       inkscape:transform-center-x="6.3227751"
       inkscape:transform-center-y="-10.683498"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
    <circle
       id="path4249"
       style="display:inline;fill:#000000;fill-opacity:1;stroke:none;filter:url(#filter4257)"
       cx="256"
       cy="256"
       r="32"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
  </g>
  <g
     inkscape:groupmode="layer"
     id="layer2"
     inkscape:label="Blades New"
     style="display:inline"
     transform="translate(2.9999996,-464.88261)">
    <path
       style="display:inline;fill:url(#linearGradient3885);fill-opacity:1;fill-rule:nonzero;stroke:none;"
       d="m 22.273322,475.18946 -4.578267,13.03322 18.029343,-3.03325 c -2.670457,-5.12847 -7.590069,-8.89703 -13.451076,-9.99997 z"
       id="path3902"
       inkscape:connector-curvature="0"
       inkscape:transform-center-x="-8.3324453"
       inkscape:transform-center-y="-11.916655" />
    <path
       inkscape:transform-center-y="2.4819199"
       inkscape:transform-center-x="-11.661293"
       inkscape:connector-curvature="0"
       id="path3905"
       d="m 36.288978,487.78017 -13.576241,2.55171 11.641554,14.09724 c 3.106154,-4.87692 3.910018,-11.02171 1.934687,-16.64895 z"
       style="display:inline;fill:url(#linearGradient3907);fill-opacity:1;fill-rule:nonzero;stroke:none;" />
    <path
       style="display:inline;fill:url(#linearGradient3911);fill-opacity:1;fill-rule:nonzero;stroke:none;"
       d="m 32.392937,506.21343 -8.997974,-10.48152 -6.387789,17.13051 c 5.776611,0.25155 11.500088,-2.12468 15.385763,-6.64899 z"
       id="path3909"
       inkscape:connector-curvature="0"
       inkscape:transform-center-x="-6.3227743"
       inkscape:transform-center-y="10.683497" />
    <path
       inkscape:transform-center-y="11.91665"
       inkscape:transform-center-x="8.3324456"
       inkscape:connector-curvature="0"
       id="path3913"
       d="m 14.481241,512.05599 4.578266,-13.03323 -18.0293426,3.03327 c 2.6704565,5.12846 7.5900694,8.89702 13.4510766,9.99996 z"
       style="display:inline;fill:url(#linearGradient3915);fill-opacity:1;fill-rule:nonzero;stroke:none;" />
    <path
       style="display:inline;fill:url(#linearGradient3919);fill-opacity:1;fill-rule:nonzero;stroke:none;"
       d="M 0.46558452,499.46529 14.041825,496.91357 2.4002718,482.81633 c -3.10615484,4.87691 -3.9100182,11.02171 -1.93468728,16.64896 z"
       id="path3917"
       inkscape:connector-curvature="0"
       inkscape:transform-center-x="11.661293"
       inkscape:transform-center-y="-2.4819151" />
    <path
       inkscape:transform-center-y="-10.683501"
       inkscape:transform-center-x="6.322774"
       inkscape:connector-curvature="0"
       id="path3921"
       d="m 4.3616255,481.03202 8.9979735,10.48151 6.38779,-17.1305 c -5.776612,-0.25154 -11.500088,2.12468 -15.3857635,6.64899 z"
       style="display:inline;fill:url(#linearGradient3923);fill-opacity:1;fill-rule:nonzero;stroke:none;" />
    <circle
       style="fill:url(#linearGradient4212);fill-opacity:1;stroke:none"
       id="path4204"
       cx="18.377281"
       cy="493.62271"
       r="2.6412084" />
  </g>
  <g
     inkscape:groupmode="layer"
     id="layer6"
     inkscape:label="Blades Blur New"
     style="display:inline;opacity:0.5"
     transform="translate(2.9999996,-464.88261)">
    <path
       style="display:inline;opacity:0.15;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;filter:url(#filter4312)"
       d="m 262.75,177.90625 c 62.52323,-10.73321 125.19451,-20.60368 187.625,-31.875 C 419.71999,95.155323 367.86385,57.206218 309.65625,44.25 294.02083,88.802083 278.38542,133.35417 262.75,177.90625 Z"
       id="path4023"
       inkscape:connector-curvature="0"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
    <path
       style="display:inline;opacity:0.25;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;filter:url(#filter4320)"
       d="m 327.03125,222.84375 c 40.52329,48.7881 80.41434,98.10187 121.40625,146.5 28.72085,-51.924 35.59327,-115.91001 17.78125,-172.71875 -46.39583,8.73958 -92.79167,17.47917 -139.1875,26.21875 z"
       id="path4025"
       inkscape:connector-curvature="0"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
    <path
       style="display:inline;opacity:0.15;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;filter:url(#filter4336)"
       d="m 253.6875,479.3125 c 59.78567,-1.05071 118.43027,-27.00522 158.75,-70.875 -30.73958,-35.83333 -61.47917,-71.66667 -92.21875,-107.5 -22.17708,59.45833 -44.35417,118.91667 -66.53125,178.375 z"
       id="path4027"
       inkscape:connector-curvature="0"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
    <path
       style="display:inline;opacity:0.15;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;filter:url(#filter4344)"
       d="M 61.4375,365.6875 C 92.010813,416.62413 143.95272,454.72618 202.34375,467.75 217.97917,423.19792 233.61458,378.64583 249.25,334.09375 186.64583,344.625 124.04167,355.15625 61.4375,365.6875 Z"
       id="path4029"
       inkscape:connector-curvature="0"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
    <path
       style="display:inline;opacity:0.25;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;filter:url(#filter4308)"
       d="M 63.25,143.21875 C 34.736871,195.07118 28.061243,258.83927 45.78125,315.375 c 46.416667,-8.73958 92.83333,-17.47917 139.25,-26.21875 -40.4375,-48.9375 -80.875,-97.875 -121.3125,-146.8125 l -0.46875,0.875 z"
       id="path4031"
       inkscape:connector-curvature="0"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
    <path
       style="display:inline;opacity:0.15;fill:#ffffff;fill-opacity:1;fill-rule:nonzero;stroke:none;filter:url(#filter4328)"
       d="m 257.71875,32.71875 c -59.39755,1.052231 -117.93041,27.160989 -158.1875,70.8125 30.75,35.84375 61.5,71.6875 92.25,107.53125 22.17708,-59.45833 44.35417,-118.916667 66.53125,-178.375 l -0.59375,0.03125 z"
       id="path4033"
       inkscape:connector-curvature="0"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
    <path
       style="display:inline;opacity:0.15;fill:#ffffff;fill-opacity:1;stroke:none;filter:url(#filter4348)"
       d="m 254.78125,234.03125 c -17.8789,-0.18647 -28.23464,25.62892 -14.5625,37.5 10.37034,10.67035 31.40662,7.50652 36.21875,-7.15625 5.24155,-12.60382 -3.31381,-29.03594 -17.28125,-30.15625 -1.45106,-0.20321 -2.91063,-0.2824 -4.375,-0.1875 z"
       id="path4214"
       inkscape:connector-curvature="0"
       transform="matrix(0.08253776,0,0,0.08253776,-2.7523864,472.49306)" />
  </g>
</svg>

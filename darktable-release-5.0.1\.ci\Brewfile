#    This file is part of darktable.
#    Copyright (C) 2016-2024 darktable developers.
#
#    darktable is free software: you can redistribute it and/or modify
#    it under the terms of the GNU General Public License as published by
#    the Free Software Foundation, either version 3 of the License, or
#    (at your option) any later version.
#
#    darktable is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#    GNU General Public License for more details.
#
#    You should have received a copy of the GNU General Public License
#    along with darktable.  If not, see <http://www.gnu.org/licenses/>.

brew 'coreutils'
brew 'cmake'
brew 'pkg-config'
brew 'adwaita-icon-theme'
brew 'desktop-file-utils'
brew 'exiv2'
brew 'gettext'
brew 'git'
brew 'glib'
brew 'gmic'
brew 'gphoto2'
brew 'gtk+3'
brew 'gtk-mac-integration'
brew 'icu4c'
brew 'imagemagick'
brew 'intltool'
brew 'iso-codes'
brew 'jpeg-turbo'
brew 'jpeg-xl'
brew 'json-glib'
brew 'lensfun'
brew 'libavif'
brew 'libheif'
brew 'libraw'
brew 'librsvg'
brew 'libsecret'
brew 'little-cms2'
brew 'lua'
brew 'libomp'
brew 'ninja'
brew 'openexr'
brew 'openjpeg'
brew 'osm-gps-map'
brew 'portmidi'
brew 'pugixml'
brew 'sdl2'
brew 'curl'
brew 'perl'
brew 'webp'

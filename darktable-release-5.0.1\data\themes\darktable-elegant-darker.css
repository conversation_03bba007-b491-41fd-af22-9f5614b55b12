/*
    This file is part of darktable,
    copyright (c) 2019 Aurélien <PERSON>

    dark<PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    darktable is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    ME<PERSON>HANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with darktable.  If not, see <http://www.gnu.org/licenses/>.
*/

@import url("darktable.css");

/* This has been tested with GTK 3.24 on Gnome */

/* hide selection frame in culling */
@define-color culling_selected_border_color transparent;

.dt_plugin_ui,
.dt_bauhaus,
.dt_bauhaus_popup,
combobox,
combobox *,
togglebutton,
togglebutton *,
notebook,
notebook *,
notebook tab,
notebook tab *,
table,
table *,
row,
row *,
frame,
frame *,
alignment,
entry,
entry *,
dialog,
dialog *,
colorswatch,
colorswatch *,
stack,
stack *,
scrollbar,
scrollbar *,
eventbox,
eventbox *,
scale,
scale *,
button,
button *,
treeview,
treeview *,
menu,
menu *,
separator,
eventbox,
eventbox *,
box,
box *
{
  font-family: "Roboto Light", "Roboto",                  /* best case scenario */
               "Segoe UI",                                /* Windows 10 default */
               "SF Pro Display Light", "SF Pro Display",  /* Mac OS X default */
               "Ubuntu Light", "Ubuntu", "IPAPGothic",    /* Ubuntu default */
               "Cantarell",                               /* Gnome default */
               "Arial Unicode MS",                        /* Unicode glyphs */
                sans-serif;                               /* default default */
}

.dt_section_label:not(#blending-box),
#blending-box .dt_section_label label,
#blending-box .dt_section_label .dt_bauhaus
{
  font-family: "Roboto Medium", "Roboto",
               "Segoe UI Semibold", "Segoe UI",
               "SF Pro Display Medium", "SF Pro Display",
               "Ubuntu Medium", "Ubuntu", "IPAPGothic",
               "Cantarell",
               "Arial Unicode MS",
                sans-serif;
}

#iop-panel-label,
#lib-panel-label
{
  font-family: "Roboto Condensed", "Roboto",
               "Segoe UI Condensed", "Segoe UI",
               "SF Pro Display", "SF Pro Display",
               "Ubuntu Condensed", "Ubuntu", "IPAPGothic",
               "Cantarell",
               "Arial Unicode MS",
                sans-serif;
}

.active_menu_item *, /* needed for some Pango issues not rendering synthetic bold for all OS */
notebook tabs,
tooltip label,
#blending-tabs,
#modules-tabs
{
  font-family: "Roboto",
               "Segoe UI",
               "SF Pro Text",
               "Ubuntu", "IPAPGothic",
               "Cantarell",
               "Arial Unicode MS",
                sans-serif;
}

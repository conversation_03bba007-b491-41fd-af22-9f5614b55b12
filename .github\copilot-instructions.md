Copilot 配置文件

## 语言设置
- **主要语言**: 中文
- **用户偏好**: 请使用中文进行交互和代码解释
- **代码注释**: 使用中文编写注释和文档

## Python 文档字符串规范
- **风格**: Google Style
- **语言**: 中文描述，结构性关键词保持英文
- **格式要求**:
  - 使用 Google Style docstring 格式
  - 描述内容使用中文
  - 保留英文结构性关键词（如 Args, Returns, Raises, Examples 等）
  - 参数和返回值描述使用中文

示例:
```python
def calculate_area(width, height):
    """计算矩形的面积。
    
    Args:
        width (float): 矩形的宽度
        height (float): 矩形的高度
    
    Returns:
        float: 矩形的面积
    
    Raises:
        ValueError: 当宽度或高度为负数时抛出异常
    
    Examples:
        >>> area = calculate_area(5, 3)
        >>> print(area)
        15
    """
    pass
```

## Python 环境

以下为当前运行的python环境的信息，在调试时可直接使用，无需再配置虚拟环境等操作。

```text
核心解释器/基础: python 3.9.13 | pip 23.0.1 | setuptools 67.8.0 | wheel 0.38.4
打包/环境: conda 22.9.0 | anaconda-client 1.11.3

数组与数值: numpy 1.26.2 | numexpr 2.8.7 | scipy 1.13.0 | mkl 2023.1.0 | mkl-service 2.4.0
数据处理: pandas 1.4.4 | pytz 2022.7 | python-dateutil 2.8.2
可视化: matplotlib 3.7.1 | seaborn 0.12.2 | bokeh 2.4.3 | hvplot 0.8.3 | holoviews 1.16.0 | plotly 5.9.0
图像/信号: pillow 9.4.0 | scikit-image 0.19.3 | imageio 2.26.0 | tifffile 2021.7.2 | rawpy 0.24.0 | opencv 4.6.0 | imagecodecs 2021.8.26
机器学习/统计: scikit-learn 1.2.2 | statsmodels 0.13.5 | imbalanced-learn 0.10.1
深度学习: pytorch 2.1.0 | torchvision 0.13.1 | tensorboard 2.15.1 | tensorboard-data-server 0.7.2
NLP / Transformers: transformers 4.29.2 | tokenizers 0.11.4 | datasets 2.12.0 | huggingface_hub 0.14.1 | sentencepiece (未列出/未安装) | sacremoses 0.0.43
并行与调度: dask 2023.4.1 | distributed 2023.4.1 | joblib 1.2.0 | numba 0.59.1 | llvmlite 0.42.0
序列化/数据格式: pyarrow 11.0.0 | h5py 3.7.0 | pytables 3.8.0 | zstd 1.5.5 | zlib 1.2.13 | lz4-c 1.9.4 | snappy 1.1.9 | brotli 1.0.9
Web/网络: requests 2.29.0 | urllib3 1.26.15 | aiohttp 3.8.3 | websockets (未安装) | flask 1.1.2
开发/质量: black 23.3.0 | flake8 6.0.0 | pylint 2.16.2 | mypy_extensions 0.4.3 | autopep8 1.6.0 | pytest 7.3.1 | pytest-cov 4.0.0 | coverage 7.2.2
交互/Notebook: jupyter 1.0.0 | jupyterlab 3.5.3 | ipykernel 6.19.2 | ipython 8.12.0 | ipywidgets 8.0.4
文档: sphinx 5.0.2 | numpydoc 1.5.0 | pdoc 14.6.0
图像色彩/ICC: colour-science 0.4.4 | lcms2 2.12 | rawpy 0.24.0
其他与项目潜在相关: rawpy 0.24.0 (RAW 图像读取) | opencv 4.6.0 (图像处理) | pillow 9.4.0 (基础图像 I/O)

安全与加密: cryptography 39.0.1 | pyopenssl 23.0.0 | bcrypt 3.2.0
实用工具: tqdm 4.65.0 | click 8.0.4 | pyyaml 6.0 | psutil 5.9.0 | tabulate 0.8.10 | rich (未安装)
缓存/加速: cachetools 5.3.2 | toolz 0.12.0 | cytoolz 0.12.0
```
